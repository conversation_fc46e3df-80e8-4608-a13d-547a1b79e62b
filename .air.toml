# .air.toml

# 构建配置
[build]
pre_cmd = ["rm -rf bin"]
cmd = "make build"  # 使用 Makefile 的 build 目标
bin = "bin/k8m -d --print-config -v 6 --enable-temp-admin"     # 可执行文件路径
include_ext = ["go"]  # 根据需要添加文件扩展名
exclude_dir = ["bin", "vendor", "ui", "images"]
exclude_file = ["*.test", ".env"]

# 颜色配置
[color]
main = "yellow"

# 监听配置
[watch]
include = ["*.go"]  # 监听 .go 文件和 assets  "ui/dist/**/*" 目录下的所有文件
exclude = ["bin/*", "vendor/*"]

# 事件钩子（可选）
[event]
post_build = "echo \"Build 完成！\""
