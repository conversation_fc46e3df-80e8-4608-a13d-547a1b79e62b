name: build and push binary to release

on:
  release:
    types: [ created, published ] # 表示在创建新的 Release 时触发
env:
  OPENAI_API_MODEL: Qwen/Qwen2.5-7B-Instruct
  OPENAI_API_URL: https://public.chatgpt.k8m.site/v1
jobs:
  build-release:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [ 18 ]
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: latest
      - name: 使用 Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "pnpm"
          cache-dependency-path: "ui/pnpm-lock.yaml"

      - name: 编译前端
        run: |
          cd ui
          pnpm install
          pnpm build

      - name: 上传到共享
        uses: actions/upload-artifact@v4
        with:
          name: workspace
          path: ui/dist

      - name: 设置go环境
        uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
          cache-dependency-path: "go.sum"

      - name: 安装 UPX
        uses: crazy-max/ghaction-upx@v3
        with:
          install-only: true

      - name: Set build time variable
        run: echo "BUILD_TIME=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_ENV

      - name: 编译后端
        run: |
          make build-all GIT_COMMIT=${{ github.sha }} GIT_TAG=${{ github.ref_name }} GIT_REPOSITORY=${{ github.repository }} BUILD_DATE=${{ env.BUILD_TIME }} VERSION=${{ github.ref_name }} MODEL=${{ env.OPENAI_API_MODEL }} API_KEY=${{ secrets.OPENAI_API_KEY }} API_URL=${{ env.OPENAI_API_URL }}

      - name: 上传二进制
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: bin/k8m*
          tag: ${{ github.ref }}
          overwrite: true
          file_glob: true
