# K8M 文档

欢迎查看K8M的文档。这里提供了所有关于K8M的使用指南和配置说明。

## 文档列表
- [lua巡检规则](lua_inspection_script.md) - 如何编写Lua巡检规则脚本。
- [数据库配置说明](database.md) - 如何配置数据库连接。
- [OIDC单点登录](oidc.md) - 如何配置使用OIDC单点登录。
- [MCP配置](mcp.md) - 如何配置使用MCP。包括开放给其他软件使用方法。
- [Github Copilot 配置MCP](mcp-github-copilot.md) - 如何配置使用MCP。包括开放给其他软件使用方法。
- [Ollama配置](ollama.md) - 如何配置使用Ollama大模型。
- [自托管/自定义大模型支持](use-self-hosted-ai.md) - 如何使用自托管的AI，包括自定义大模型的配置方法。
- [管理员账户配置](temp-admin-config.md) - 临时管理员账户的设置方法和注意事项。
- [如何开启两步验证](2fa.md) - 如何开启两步验证。
- [自定义菜单配置](custom-menu.md) - 如何为用户组配置自定义菜单，包括菜单编辑器的使用方法。
- [变量配置选项说明](param-config.md) - 配置选项的说明。
- [路由结构图](route_structure.md) - K8M系统的API路由结构图。

  