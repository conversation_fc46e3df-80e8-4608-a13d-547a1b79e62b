# K8M 自定义菜单使用说明

## 概述

K8M 支持为不同用户组配置自定义菜单，通过可视化菜单编辑器，管理员可以灵活定制每个用户组的菜单结构和功能权限。

## 菜单配置优先级

- **用户组自定义菜单** > **系统默认菜单**
- 如果用户组设置了菜单数据，系统将使用该用户组的自定义菜单配置，覆盖默认菜单
- 如果用户组未设置菜单数据，系统将使用默认的菜单配置
- 当用户归属多个用户组且出现菜单冲突时，按照用户第一个用户组的菜单配置为准

## 配置步骤

### 1. 进入用户组管理

1. 登录 K8M 管理后台
2. 点击左侧菜单中的 `平台设置` → `用户组管理`

### 2. 设置用户组菜单

1. 在用户组列表中，找到需要配置菜单的用户组
2. 点击该用户组行的 **菜单设置** 按钮（菜单图标）
3. 在弹出的对话框中，点击 **"打开菜单编辑器"** 按钮

### 3. 使用菜单编辑器

#### 基本操作

- **新增根菜单**：点击左上角的 ➕ 按钮添加顶级菜单项
- **编辑菜单**：点击菜单项后的 ✏️ 图标编辑该菜单
- **删除菜单**：点击菜单项后的 🗑️ 图标删除该菜单
- **添加子菜单**：点击菜单项后的 ➕ 图标添加子菜单
- **拖拽排序**：直接拖动菜单项可以调整顺序或层级

#### 菜单配置项说明

| 配置项 | 说明 | 示例 |
|--------|------|------|
| 菜单名称 | 显示在界面上的菜单文字 | "集群管理"、"应用部署" |
| 图标 | Font Awesome 图标类名 | `fa-server`、`fa-cube` |
| 点击事件 | 菜单点击后的行为类型 | URL跳转 或 自定义事件 |
| URL | 当选择URL跳转时的目标地址 | `/#/cluster/list` |
| 自定义事件 | 当选择自定义事件时的JavaScript代码 | `window.open('/help')` |
| 排序 | 同级菜单的显示顺序 | 1, 2, 3... |
| 显示表达式 | 控制菜单显示的JavaScript表达式 | `true`、`user.role === 'admin'` |

#### 显示表达式详解

显示表达式支持以下预定义函数和表达式：

- `true` 或 `false`：直接控制显示/隐藏
- `contains('admin', user.role)`：检查用户角色是否包含指定字符串
- `isGatewayAPISupported()==true`：检查集群是否支持Gateway API
- `isIstioSupported()==true`：检查集群是否支持Istio
- `isOpenKruiseSupported()==true`：检查集群是否支持OpenKruise
- `isPlatformAdmin()==true`：检查用户是否为平台管理员

### 4. 高级功能

#### 预览模式
- 点击右上角的 👁️ 图标可以预览菜单实际效果
- 预览模式下可以看到菜单的最终显示效果

#### 历史记录
- 点击右上角的 🕐 图标查看菜单修改历史
- 支持恢复到任意历史版本
- 支持删除不需要的历史记录
- 支持复制历史版本的JSON配置

#### 导入/导出
- **复制配置**：点击 📋 图标复制当前菜单的JSON配置
- **查看JSON**：点击 📄 图标查看完整的JSON配置
- **导入菜单**：点击 📥 图标导入已有的菜单JSON配置

### 5. 保存配置

1. 在菜单编辑器中完成菜单设计
2. 复制编辑器生成的最终JSON配置
3. 返回用户组菜单设置对话框
4. 将JSON配置粘贴到编辑器中
5. 点击 **"保存配置"** 按钮

## 菜单JSON配置示例

```json
[
  {
    "key": "menu_1640000000000_abc123",
    "title": "集群管理",
    "icon": "fa-server",
    "eventType": "url",
    "url": "/#/cluster/list",
    "order": 1,
    "show": "true",
    "children": [
      {
        "key": "menu_1640000000001_def456",
        "title": "节点管理",
        "icon": "fa-desktop",
        "eventType": "url",
        "url": "/#/cluster/nodes",
        "order": 1,
        "show": "isPlatformAdmin()==true"
      }
    ]
  },
  {
    "key": "menu_1640000000002_ghi789",
    "title": "应用部署",
    "icon": "fa-cube",
    "eventType": "custom",
    "customEvent": "window.location.href='/#/apps'",
    "order": 2,
    "show": "contains('admin', user.role)"
  }
]
```

## 常见问题

### Q: 如何创建多级菜单？
A: 在菜单编辑器中，点击父菜单项后的 ➕ 图标即可添加子菜单。支持无限层级嵌套。

### Q: 如何控制菜单的显示权限？
A: 通过"显示表达式"字段，使用JavaScript表达式控制菜单的显示条件，如 `isPlatformAdmin()==true` 只对平台管理员显示。

### Q: 菜单配置出错怎么办？
A: 可以通过历史记录功能恢复到之前的正常版本，或者重新导入正确的JSON配置。

### Q: 如何快速复制其他用户组的菜单配置？
A: 在一个用户组的菜单设置中复制JSON配置，然后在另一个用户组的菜单设置中粘贴即可。

## 注意事项

1. **权限控制**：只有平台管理员可以配置用户组菜单
2. **生效时间**：菜单配置保存后，用户需要重新登录才能看到新的菜单结构
3. **备份建议**：重要的菜单配置建议备份JSON文件，以防意外丢失
4. **测试建议**：配置完成后建议使用对应用户组的测试账号验证菜单功能
5. **性能考虑**：避免创建过深的菜单层级，建议不超过3-4级
