# K8M 前端模块

## 开发环境准备

确保已安装以下工具：
- Node.js (推荐 v16 或更高版本)
- pnpm (推荐)

## 安装依赖

进入前端目录：

```bash
cd ui
```

使用 pnpm 安装依赖（推荐）：

```bash
pnpm install
```

或使用 npm 安装依赖：

```bash
npm install
```

## 启动开发服务器

使用 pnpm 启动（推荐）：

```bash
pnpm dev
```

或使用 npm 启动：

```bash
npm run dev
```

启动成功后，在浏览器中访问：

http://localhost:3000

## 注意事项

1. 首次运行需要先安装依赖
2. 确保端口 3000 未被其他程序占用
3. 如遇到依赖安装问题，可以尝试删除 node_modules 目录后重新安装