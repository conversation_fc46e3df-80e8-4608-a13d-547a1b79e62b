{"type": "page", "title": "用户组管理", "body": [{"type": "alert", "level": "info", "body": "<div class='alert alert-info'><p><strong>菜单配置说明：</strong></p><ul><li><strong>自定义菜单：</strong>如果用户组设置了菜单数据，系统将使用该用户组的自定义菜单配置，覆盖默认菜单</li><li><strong>默认菜单：</strong>如果用户组未设置菜单数据，系统将使用默认的菜单配置</li><li><strong>多用户组冲突：</strong>当用户归属多个用户组且出现菜单冲突时，按照用户第一个用户组的菜单配置为准</li><li><strong>菜单优先级：</strong>用户组自定义菜单 > 系统默认菜单</li></ul></div>", "className": "mb-3"}, {"type": "crud", "id": "groupCRUD", "name": "groupCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "button", "icon": "fas fa-plus text-primary", "actionType": "drawer", "label": "新建用户组", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "新建用户组  (ESC 关闭)", "body": {"type": "form", "api": "post:/admin/user_group/save", "body": [{"type": "input-text", "name": "group_name", "label": "组名", "required": true, "placeholder": "请输入用户组名称", "validations": {"minLength": 2, "maxLength": 20}}, {"type": "select", "name": "role", "label": "角色", "required": true, "options": [{"label": "普通用户", "value": "guest"}, {"label": "平台管理员", "value": "platform_admin"}], "placeholder": "请选择角色"}, {"type": "textarea", "name": "description", "label": "描述", "placeholder": "请输入用户组描述"}, {"type": "alert", "level": "success", "body": "<div class='alert alert-info'><p><strong>普通用户：</strong>无集群权限，除非在集群中进行显式授权。</p><p><strong>平台管理员：</strong>可以管理平台配置、用户权限等系统级设置。</p></div>"}, {"type": "alert", "level": "success", "body": "<div class='alert alert-info'><p><strong>普通用户需要授权，不授权看不到任何集群。授权规则如下：</strong></p><p><strong>集群管理员：</strong>可以管理和操作所有集群资源，包括创建、修改、删除等操作。</p><p><strong>集群只读：</strong>仅可查看集群资源信息，无法进行修改操作。</p><p><strong>Exec权限：</strong>具有进入容器内，执行命令的权限</p></div>"}], "submitText": "保存", "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "groupCRUD"}, {"actionType": "closeDrawer"}]}}}}}, "reload", "bulkActions"], "api": "get:/admin/user_group/list", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "icon": "fas fa-edit text-primary", "actionType": "drawer", "tooltip": "编辑用户组", "drawer": {"title": "编辑用户组", "body": {"type": "form", "api": "post:/admin/user_group/save", "body": [{"type": "hidden", "name": "id"}, {"type": "input-text", "name": "group_name", "label": "组名"}, {"type": "select", "name": "role", "label": "角色", "required": true, "options": [{"label": "普通用户", "value": "guest"}, {"label": "平台管理员", "value": "platform_admin"}], "placeholder": "请选择角色"}, {"type": "textarea", "name": "description", "label": "描述"}, {"type": "alert", "level": "success", "body": "<div class='alert alert-info'><p><strong>普通用户：</strong>无集群权限，除非在集群中进行显式授权。</p><p><strong>平台管理员：</strong>可以管理平台配置、用户权限等系统级设置。</p></div>"}, {"type": "alert", "level": "success", "body": "<div class='alert alert-info'><p><strong>普通用户需要授权，不授权看不到任何集群。授权规则如下：</strong></p><p><strong>集群管理员：</strong>可以管理和操作所有集群资源，包括创建、修改、删除等操作。</p><p><strong>集群只读：</strong>仅可查看集群资源信息，无法进行修改操作。</p><p><strong>Exec权限：</strong>具有进入容器内，执行命令的权限</p></div>"}]}}}, {"type": "button", "icon": "fas fa-bars text-info", "actionType": "dialog", "tooltip": "设置菜单", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "title": "设置用户组菜单配置 （ESC 关闭）", "size": "lg", "body": {"type": "form", "api": "post:/admin/user_group/save_menu", "body": [{"type": "hidden", "name": "id"}, {"type": "group", "body": [{"type": "button", "label": "打开菜单编辑器", "icon": "fas fa-external-link-alt", "level": "info", "actionType": "url", "url": "/#/MenuEditor", "blank": true, "className": "mb-2"}]}, {"type": "editor", "name": "menu_data", "size": "xl", "allowFullscreen": true, "placeholder": "请粘贴菜单JSON配置", "language": "json", "value": "${menu_data}", "options": {"wordWrap": "on", "scrollbar": {"vertical": "auto"}}}, {"type": "alert", "level": "warning", "body": "<div class='alert alert-warning'><p><strong>使用说明：</strong></p><p>点击上方的「打开菜单编辑器」按钮，在新标签页中打开可视化菜单编辑器，在菜单编辑器中设计和配置您需要的菜单结构，完成菜单设计后，复制编辑器生成的最终JSON配置。将复制的JSON配置粘贴到上方的编辑器</p></div>", "className": "mb-3"}], "submitText": "保存配置", "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "groupCRUD"}, {"actionType": "closeDialog"}]}}}}}, {"type": "button", "icon": "fas fa-trash text-danger", "actionType": "ajax", "confirmText": "确定删除该用户组？", "api": "delete:/admin/user_group/delete/${id}"}]}, {"name": "group_name", "label": "组名", "sortable": true}, {"name": "description", "label": "描述"}, {"name": "role", "label": "角色", "type": "mapping", "map": {"guest": "普通用户", "platform_admin": "平台管理员"}, "searchable": {"type": "select", "name": "role", "clearable": true, "label": "角色", "placeholder": "请选择角色", "options": [{"label": "集群管理员", "value": "cluster_admin"}, {"label": "集群只读", "value": "cluster_readonly"}, {"label": "平台管理员", "value": "platform_admin"}]}}, {"name": "menu_data", "label": "菜单状态", "type": "mapping", "map": {"": "", "*": "<span class='label label-success'>已设置</span>"}}, {"name": "created_at", "label": "创建时间", "type": "datetime"}], "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": "/admin/user_group/delete/${ids}"}]}]}