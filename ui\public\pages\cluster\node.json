{"type": "page", "data": {"kind": "Node", "group": "", "version": "v1"}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}, {"type": "button", "label": "属性文档", "level": "link", "icon": "fas fa-book-open text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 属性文档（ESC 关闭）", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/kind/$kind/group/$group/version/$version", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}}, {"type": "button", "label": "指南", "level": "link", "icon": "fas fa-lightbulb text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}"}}]}}, {"type": "button", "label": "智检", "level": "link", "icon": "fas fa-walking text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI 智能巡检 ${kind} （ESC 关闭）", "body": [{"type": "k8sGPT", "api": "/k8s/k8s_gpt/kind/${kind}/run"}]}}]}, {"type": "container", "className": "floating-toolbar-right", "body": [{"type": "wrapper", "style": {"display": "inline-flex"}, "body": [{"type": "form", "mode": "inline", "wrapWithPanel": false, "body": [{"label": "集群", "type": "select", "multiple": false, "name": "cluster", "id": "cluster", "searchable": true, "source": "/params/cluster/option_list", "value": "${ls:cluster}", "onEvent": {"change": {"actions": [{"actionType": "custom", "script": "localStorage.setItem('cluster', event.data.value)"}, {"actionType": "custom", "script": "window.location.reload();"}]}}}]}]}]}, {"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload", {"type": "button", "label": "资源用量", "actionType": "drawer", "tooltip": "资源用量 kubectl top nodes", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "节点资源用量（ESC 关闭）", "size": "xl", "body": [{"type": "crud", "id": "topNodeCRUD", "name": "topNodeCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "loadDataOnce": true, "syncLocation": false, "initFetch": true, "perPage": 10, "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "api": "get:/k8s/node/top/list", "columns": [{"name": "name", "label": "节点名称", "type": "text", "sortable": true, "searchable": true}, {"name": "cpu", "label": "cpu（核）", "type": "text", "sortable": true}, {"name": "cpu_fraction", "label": "CPU资源<br><span class='text-gray-500 text-xs'>实时用量占比</span>", "type": "container", "width": "150px", "sortable": true, "body": [{"type": "progress", "label": "realtime", "value": "<%= data.cpu_fraction && Number(data.cpu_fraction) !== 0 ? data.cpu_fraction : 0 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"]}]}, {"name": "memory", "label": "内存（Mi）", "type": "text", "sortable": true}, {"name": "memory_fraction", "label": "内存资源<br><span class='text-gray-500 text-xs'>实时用量占比</span>", "type": "container", "width": "150px", "sortable": true, "body": [{"type": "progress", "label": "realtime", "value": "<%= data.memory_fraction && Number(data.memory_fraction) !== 0 ? data.memory_fraction : 0 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"]}]}]}]}}, "bulkActions"], "loadDataOnce": false, "syncLocation": false, "perPage": 10, "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/batch/remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"label": "强制删除", "actionType": "ajax", "confirmText": "确定要批量强制删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/force_remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"label": "节点隔离", "actionType": "ajax", "confirmText": "确定要批量隔离?", "api": {"url": "/k8s/node/batch/cordon", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }"}}}, {"label": "解除隔离", "actionType": "ajax", "confirmText": "确定要批量解除隔离?", "api": {"url": "/k8s/node/batch/uncordon", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }"}}}, {"label": "驱逐", "actionType": "ajax", "confirmText": "确定要批量驱逐?", "api": {"url": "/k8s/node/batch/drain", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }"}}}], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "api": "post:/k8s/$kind/group/$group/version/$version/list", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/$kind/group/$group/version/$version/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "name": "${metadata.name}", "namespace": "${metadata.namespace}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group/${group}/version/${version}/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}]}], "actions": []}}, {"type": "dropdown-button", "level": "link", "buttons": [{"type": "link", "icon": "fa-brands fa-docker text-primary", "body": "容器列表", "blank": false, "href": "/#/ns/pod?spec[nodeName]=${metadata.name}"}, {"type": "link", "icon": "fa fa-terminal text-primary", "body": "节点终端", "blank": true, "href": "/#/NodeExec?nodeName=${metadata.name}"}, {"label": "资源用量", "type": "button", "icon": "fas fa-percentage text-primary", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "title": "资源使用详情 （ESC 关闭）", "size": "lg", "body": {"type": "service", "api": "get:/k8s/node/usage/name/${metadata.name}", "body": {"type": "table", "columns": [{"label": "资源类型", "name": "resourceType"}, {"label": "总量", "name": "total"}, {"label": "请求量", "name": "request"}, {"label": "请求占比 (%)", "name": "requestFraction", "type": "tpl", "tpl": "<span class='label <% if (data.requestFraction > 80) { %>label-danger<% } else if (data.requestFraction > 50) { %>label-warning<% } else { %>label-success<% } %>'> <%= data.requestFraction %> % </span>"}, {"label": "限制量", "name": "limit"}, {"label": "限制占比 (%)", "name": "limitFraction", "type": "tpl", "tpl": "<span class='label <% if (data.limitFraction > 80) { %>label-danger<% } else if (data.limitFraction > 50) { %>label-warning<% } else { %>label-success<% } %>'> <%= data.limitFraction %> % </span>"}]}}}}, {"type": "button", "icon": "fas fa-calendar-alt text-primary", "label": "查看事件", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "查看事件   (ESC 关闭)", "body": [{"type": "crud", "id": "detailEvent", "name": "detailEvent", "headerToolbar": ["reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "loadDataOnce": true, "syncLocation": false, "perPage": 10, "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name/event", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "icon": "fas fa-brain text-primary", "label": "AI问诊", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI 查询", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/event", "params": {"note": "${note}", "source": "${source}", "reason": "${reason}", "reportingController": "${reportingController}", "type": "${type}", "regardingKind": "${kind}"}}]}, "visibleOn": "type === 'Warning'"}], "toggled": true}, {"name": "type", "label": "类型", "filterable": {"options": [{"label": "正常 ", "value": "Normal"}, {"label": "告警 ", "value": "Warning"}]}, "type": "mapping", "map": {"Normal": "<span class='label label-success'>正常</span>", "Warning": "<span class='label label-danger'>告警</span>"}}, {"name": "reason", "label": "原因", "type": "text"}, {"name": "field", "label": "关联字段", "type": "tpl", "tpl": "${regarding.fieldPath}"}, {"name": "source", "label": "事件来源", "type": "tpl", "tpl": "${reportingController} ${reportingInstance}"}, {"name": "note", "label": "说明", "type": "text", "searchable": true}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}}]}], "toggled": true}, {"name": "status.addresses", "label": "节点", "type": "each", "items": {"type": "tpl", "tpl": "${address}<br>"}, "searchable": {"type": "input-text", "name": "status.addresses.address", "label": "IP", "placeholder": "输入IP"}}, {"name": "roles", "label": "节点角色", "type": "nodeRoles", "width": "150px"}, {"name": "status", "label": "硬件配置", "type": "tpl", "tpl": "${status.capacity.cpu}C/${status.capacity.memory|autoConvertMemory}"}, {"name": "spec.unschedulable", "label": "调度状态", "type": "tpl", "tpl": "<span class='label label-${spec.unschedulable === true ? 'danger' : 'success'}'>${spec.unschedulable === true ? '停止调度' : '调度正常'}</span>", "searchable": {"type": "select", "clearable": true, "options": [{"label": "停止调度", "value": "true"}]}}, {"name": "status.conditions", "label": "条件", "type": "k8sTextConditions"}, {"name": "metadata.labels", "label": "标签", "type": "tpl", "tpl": "${metadata.labels ? '<i class=\"fa fa-tags text-primary\"></i>' : '<i class=\"fa fa-tags text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 标签 (ESC 关闭)", "name": "dialog_labels", "size": "lg", "closeOnEsc": true, "closeOnOutside": true, "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_labels/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "labels", "draggable": false, "value": "${metadata.labels}"}]}]}}]}}, "style": {"cursor": "pointer"}, "searchable": {"type": "input-text", "name": "metadata.labels", "clearable": true, "label": "模糊标签", "placeholder": "模糊搜索k、v"}}, {"name": "metadata.annotations", "label": "注解", "type": "tpl", "tpl": "${metadata.annotations|filterAnnotations|showAnnotationIcon|isTrue:'<i class=\"fa fa-note-sticky text-primary\"></i>':'<i class=\"fa fa-note-sticky text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 注解 (ESC 关闭)", "name": "dialog_annotations", "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_annotations/ns/$metadata.namespace/name/$metadata.name", "initApi": "get:/k8s/$kind/group/$group/version/$version/annotations/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "annotations", "draggable": false, "value": "${annotations}"}]}], "size": "lg", "closeOnEsc": true, "closeOnOutside": true}}]}}, "style": {"cursor": "pointer"}}, {"name": "spec.taints", "label": "污点", "type": "tpl", "tpl": "${spec.taints ? '<i class=\"fa-solid fa-exclamation-triangle text-primary\"></i>' : '<i class=\"fa-solid fa-exclamation-triangle text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${metadata.name} 污点 (ESC 关闭)", "body": [{"type": "panel", "title": "调度规则：如何使用 污点", "body": [{"type": "button", "label": "什么是污点", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是污点（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是污点"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>污点规则:</strong> Pod 必须容忍节点上的污点（Taints），才能被调度到该节点。污点由 key、value 和 effect 三部分组成，其中 key 是污点的键，value 是污点的值，effect 表示污点的影响。</div><div><strong>匹配规则:</strong><br>1. Pod 必须具有与节点上的污点匹配的容忍规则，才能允许调度。<br>2. 容忍规则中，key 和 effect 必须与节点上的污点完全一致，value 匹配时由容忍规则中的 operator 决定。</div><div><strong>调度行为:</strong> 只有当 Pod 的容忍规则与节点污点的 key 和 effect 匹配，并且 value 满足容忍规则中的 operator 时，Pod 才会被调度到该节点，否则调度会被阻止。</div></div>"}]}, {"type": "crud", "api": "/k8s/node/list_taints/name/$metadata.name", "quickSaveItemApi": "/k8s/node/update_taints/name/$metadata.name", "headerToolbar": [{"type": "button", "label": "新增污点", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": [{"type": "form", "mode": "horizontal", "id": "taint_add_form", "api": "/k8s/node/add_taints/name/$metadata.name", "body": [{"type": "input-text", "name": "key", "label": "键", "required": true}, {"name": "value", "type": "input-text", "label": "值"}, {"name": "effect", "label": "效果", "required": true, "value": "NoSchedule", "type": "select", "options": [{"label": "禁止调度", "value": "NoSchedule"}, {"label": "优先不调度", "value": "PreferNoSchedule"}, {"label": "不可执行", "value": "NoExecute"}]}]}]}}], "showIndex": true, "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/node/delete_taints/name/$metadata.name"}], "toggled": true}, {"name": "key", "label": "键"}, {"name": "value", "label": "值", "placeholder": "-", "quickEdit": {"type": "input-text", "saveImmediately": true}}, {"name": "effect", "label": "效果", "type": "mapping", "map": {"NoSchedule": "<span class='label label-warning'>禁止调度</span>", "PreferNoSchedule": "<span class='label label-danger'>优先不调度</span>", "NoExecute": "<span class='label label-danger'>不可执行</span>"}, "quickEdit": {"type": "select", "saveImmediately": true, "options": [{"label": "禁止调度", "value": "NoSchedule"}, {"label": "优先不调度", "value": "PreferNoSchedule"}, {"label": "不可执行", "value": "NoExecute"}]}}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "realtime", "label": "实时用量<br><span class='text-gray-500 text-xs'>CPU(m毫核)/内存(Mi)</span>", "type": "tpl", "tpl": "<%= ( data.metadata.annotations && data.metadata.annotations['cpu.realtime'] && Number(data.metadata.annotations['cpu.realtime']) !== 0 ? data.metadata.annotations['cpu.realtime']  : '') + ' / ' + (data.metadata.annotations && data.metadata.annotations['memory.realtime'] && Number(data.metadata.annotations['memory.realtime']) !== 0? data.metadata.annotations['memory.realtime'] : '') %>"}, {"name": "resource", "label": "CPU资源<br><span class='text-gray-500 text-xs'>请求用量占比<br>上限用量占比<br>实时用量占比</span>", "type": "container", "width": "150px", "body": [{"type": "progress", "label": "request", "value": "<%= data.metadata.annotations['cpu.requestFraction'] && Number(data.metadata.annotations['cpu.requestFraction']) !== 0 ? data.metadata.annotations['cpu.requestFraction'] : 100 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"], "visibleOn": "this.metadata.annotations && this.metadata.annotations['cpu.requestFraction']"}, {"type": "progress", "label": "limit", "value": "<%= data.metadata.annotations['cpu.limitFraction'] && Number(data.metadata.annotations['cpu.limitFraction']) !== 0 ? data.metadata.annotations['cpu.limitFraction'] : 100 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"], "visibleOn": "this.metadata.annotations && this.metadata.annotations['cpu.limitFraction']"}, {"type": "progress", "label": "realtime", "value": "<%= data.metadata.annotations['cpu.realtimeFraction'] && Number(data.metadata.annotations['cpu.realtimeFraction']) !== 0 ? data.metadata.annotations['cpu.realtimeFraction'] : 0 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"], "visibleOn": "this.metadata.annotations && this.metadata.annotations['cpu.realtimeFraction']"}]}, {"name": "resource", "label": "内存资源<br><span class='text-gray-500 text-xs'>请求用量占比<br>上限用量占比<br>实时用量占比</span>", "type": "container", "width": "150px", "body": [{"type": "progress", "label": "request", "value": "<%= data.metadata.annotations['memory.requestFraction'] && Number(data.metadata.annotations['memory.requestFraction']) !== 0 ? data.metadata.annotations['memory.requestFraction'] : 100 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"], "visibleOn": "this.metadata.annotations && this.metadata.annotations['memory.requestFraction']"}, {"type": "progress", "label": "limit", "value": "<%= data.metadata.annotations['memory.limitFraction'] && Number(data.metadata.annotations['memory.limitFraction']) !== 0 ? data.metadata.annotations['memory.limitFraction'] : 100 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"], "visibleOn": "this.metadata.annotations && this.metadata.annotations['memory.limitFraction']"}, {"type": "progress", "label": "realtime", "value": "<%= data.metadata.annotations['memory.realtimeFraction'] && Number(data.metadata.annotations['memory.realtimeFraction']) !== 0 ? data.metadata.annotations['memory.realtimeFraction'] : 0 %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"], "visibleOn": "this.metadata.annotations && this.metadata.annotations['memory.realtimeFraction']"}]}, {"name": "ip", "label": "IP资源<br><span class='text-gray-500 text-xs'>当前/可用/总数</span>", "type": "container", "width": "150px", "body": [{"name": "metadata.annotations['ip.usage.total']", "label": "IP资源", "visibleOn": "${metadata.annotations['ip.usage.total']}", "type": "tpl", "tpl": "${metadata.annotations['ip.usage.used']}/${metadata.annotations['ip.usage.available']}/${metadata.annotations['ip.usage.total']}"}, {"type": "progress", "label": "IP Usage Progress", "value": "<%= (data.metadata.annotations['ip.usage.used'] / data.metadata.annotations['ip.usage.total'] * 100).toFixed(2) %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"]}]}, {"name": "pod", "label": "Pod数量<br><span class='text-gray-500 text-xs'>当前/可用/总数</span>", "type": "container", "width": "150px", "body": [{"name": "metadata.annotations['pod.count.total']", "label": "Pod数量", "visibleOn": "${metadata.annotations['pod.count.total']}", "type": "tpl", "tpl": "${metadata.annotations['pod.count.used']}/${metadata.annotations['pod.count.available']}/${metadata.annotations['pod.count.total']}"}, {"type": "progress", "label": "Pod Count Progress", "value": "<%= (data.metadata.annotations['pod.count.used'] / data.metadata.annotations['pod.count.total'] * 100).toFixed(2) %>", "placeholder": "N/A", "animate": true, "width": "100px", "map": ["bg-success", "bg-success", "bg-success", "bg-warning", "bg-danger"]}]}, {"name": "status.images", "label": "容器镜像", "type": "tpl", "tpl": "${count(status.images)}"}, {"name": "status.nodeInfo.architecture", "label": "节点架构", "type": "text"}, {"name": "version", "label": "k8s版本", "type": "tpl", "tpl": "${status.nodeInfo.kubeletVersion ? 'kubelet:'+status.nodeInfo.kubeletVersion : ''}<br>${status.nodeInfo.kernelVersion ? 'kernel:'+status.nodeInfo.kernelVersion : ''}<br>${status.nodeInfo.containerRuntimeVersion ? 'cri:'+status.nodeInfo.containerRuntimeVersion : ''}"}, {"name": "status.nodeInfo.osImage", "label": "操作系统", "width": "200px", "type": "text"}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px", "sortable": true, "searchable": {"type": "input-text", "name": "metadata.name", "clearable": true, "label": "名称", "placeholder": "输入名称"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}, {"name": "unique_labels", "label": "精确标签", "visibleOn": "1==2", "searchable": {"type": "select", "clearable": true, "searchable": true, "source": "/k8s/node/labels/unique_labels", "creatable": true, "addControls": [{"type": "text", "name": "label", "label": "自定义标签", "placeholder": "k=v,如果不写等号，则只搜索k"}]}}]}]}