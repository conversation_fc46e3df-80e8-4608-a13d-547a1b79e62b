{"type": "page", "data": {"kind": "Namespace", "group": "", "version": "v1"}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}, {"type": "button", "label": "属性文档", "level": "link", "icon": "fas fa-book-open text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 属性文档（ESC 关闭）", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/kind/$kind/group/$group/version/$version", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}}, {"type": "button", "label": "指南", "level": "link", "icon": "fas fa-lightbulb text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}"}}]}}, {"label": "创建", "icon": "fas fa-dharmachakra text-primary", "type": "button", "level": "link", "actionType": "url", "blank": true, "url": "/#/apply/apply?kind=${kind}"}]}, {"type": "container", "className": "floating-toolbar-right", "body": [{"type": "wrapper", "style": {"display": "inline-flex"}, "body": [{"type": "form", "mode": "inline", "wrapWithPanel": false, "body": [{"label": "集群", "type": "select", "multiple": false, "name": "cluster", "id": "cluster", "searchable": true, "source": "/params/cluster/option_list", "value": "${ls:cluster}", "onEvent": {"change": {"actions": [{"actionType": "custom", "script": "localStorage.setItem('cluster', event.data.value)"}, {"actionType": "custom", "script": "window.location.reload();"}]}}}]}]}]}, {"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload", "bulkActions"], "loadDataOnce": false, "syncLocation": false, "perPage": 10, "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/batch/remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"label": "强制删除", "actionType": "ajax", "confirmText": "确定要批量强制删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/force_remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}], "api": "post:/k8s/$kind/group/$group/version/$version/list", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/$kind/group/$group/version/$version/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "name": "${metadata.name}", "namespace": "${metadata.namespace}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group/${group}/version/${version}/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}]}], "actions": []}}, {"type": "dropdown-button", "level": "link", "buttons": [{"type": "button", "icon": "fa-solid fa-plus text-success", "label": "创建资源配额", "actionType": "dialog", "dialog": {"title": "创建资源配额", "size": "lg", "closeOnEsc": true, "closeOnOutside": true, "body": [{"type": "form", "api": "post:/k8s/ResourceQuota/create", "body": [{"type": "input-text", "name": "name", "label": "名称", "required": true, "placeholder": "请输入资源配额名称"}, {"type": "input-text", "name": "metadata.namespace", "label": "命名空间", "value": "${metadata.name}", "readOnly": true}, {"type": "divider", "title": "资源限制"}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.requests.memory", "label": "内存请求", "placeholder": "例如: 2", "addOn": "Gi"}, {"type": "input-text", "name": "spec.hard.limits.memory", "label": "内存上限", "placeholder": "例如: 10", "addOn": "Gi"}]}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.requests.cpu", "label": "CPU请求", "placeholder": "例如: 500", "addOn": "微核"}, {"type": "input-text", "name": "spec.hard.limits.cpu", "label": "CPU上限", "placeholder": "例如: 1000", "addOn": "微核"}]}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.requests.storage", "label": "存储请求", "placeholder": "例如: 10", "addOn": "Gi"}, {"type": "input-text", "name": "spec.hard.pods", "label": "Pod数量", "placeholder": "例如: 10", "addOn": "个"}]}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.configmaps", "label": "配置映射数量", "placeholder": "例如: 10", "addOn": "个"}, {"type": "input-text", "name": "spec.hard.replicationcontrollers", "label": "副本控制器数量", "placeholder": "例如: 10", "addOn": "个"}]}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.resourcequotas", "label": "资源配额数量", "placeholder": "例如: 5", "addOn": "个"}, {"type": "input-text", "name": "spec.hard.services", "label": "服务数量", "placeholder": "例如: 10", "addOn": "个"}]}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.loadbalancers", "label": "负载均衡器数量", "placeholder": "例如: 5", "addOn": "个"}, {"type": "input-text", "name": "spec.hard.nodeports", "label": "节点端口数量", "placeholder": "例如: 5", "addOn": "个"}]}, {"type": "group", "body": [{"type": "input-text", "name": "spec.hard.secrets", "label": "保密字典数量", "placeholder": "例如: 10", "addOn": "个"}, {"type": "input-text", "name": "spec.hard.persistentvolumeclaims", "label": "持久卷声明数量", "placeholder": "例如: 5", "addOn": "个"}]}]}]}}, {"type": "link", "icon": "fa-solid fa-chart-pie text-primary", "body": "查看资源配额", "blank": false, "href": "/#/ns/resource_quota?metadata[namespace]=${metadata.name}"}, {"type": "button", "icon": "fa-solid fa-plus text-success", "label": "创建限制范围", "actionType": "dialog", "dialog": {"title": "创建限制范围", "size": "lg", "closeOnEsc": true, "closeOnOutside": true, "body": [{"type": "form", "api": "post:/k8s/LimitRange/create", "body": [{"type": "input-text", "name": "name", "label": "名称", "required": true, "placeholder": "请输入限制范围名称"}, {"type": "input-text", "name": "metadata.namespace", "label": "命名空间", "value": "${metadata.name}", "readOnly": true}, {"type": "divider", "title": "限制范围配置"}, {"type": "combo", "name": "spec.limits", "label": "限制项", "multiple": true, "multiLine": true, "minLength": 1, "addButtonText": "添加限制项", "items": [{"type": "select", "name": "type", "label": "类型", "required": true, "options": [{"label": "容器", "value": "Container"}, {"label": "Pod", "value": "Pod"}]}, {"type": "group", "body": [{"type": "input-text", "name": "default.cpu", "label": "默认CPU", "placeholder": "例如: 500", "addOn": "微核", "columnClassName": "col-sm-6"}, {"type": "input-text", "name": "default.memory", "label": "默认内存", "placeholder": "例如: 2", "addOn": "<PERSON>", "columnClassName": "col-sm-6"}]}, {"type": "group", "body": [{"type": "input-text", "name": "defaultRequest.cpu", "label": "默认请求CPU", "placeholder": "例如: 200", "addOn": "微核", "columnClassName": "col-sm-6"}, {"type": "input-text", "name": "defaultRequest.memory", "label": "默认请求内存", "placeholder": "例如: 1", "addOn": "<PERSON>", "columnClassName": "col-sm-6"}]}, {"type": "group", "body": [{"type": "input-text", "name": "min.cpu", "label": "最小CPU", "placeholder": "例如: 100", "addOn": "微核", "columnClassName": "col-sm-6"}, {"type": "input-text", "name": "min.memory", "label": "最小内存", "placeholder": "例如: 0.5", "addOn": "<PERSON>", "columnClassName": "col-sm-6"}]}, {"type": "group", "body": [{"type": "input-text", "name": "max.cpu", "label": "最大CPU", "placeholder": "例如: 1000", "addOn": "微核", "columnClassName": "col-sm-6"}, {"type": "input-text", "name": "max.memory", "label": "最大内存", "placeholder": "例如: 4", "addOn": "<PERSON>", "columnClassName": "col-sm-6"}]}]}]}]}}, {"type": "link", "icon": "fa-solid fa-compress text-primary", "body": "查看限制范围", "blank": false, "href": "/#/ns/limit_range?metadata[namespace]=${metadata.name}"}]}], "toggled": true}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px", "sortable": true, "searchable": {"type": "input-text", "name": "metadata.name", "clearable": true, "label": "名称", "placeholder": "输入名称"}}, {"name": "status.phase", "label": "状态", "type": "mapping", "map": {"Active": "<span class='label label-success'>活跃</span>", "Terminating": "<span class='label label-danger'>正在删除</span>", "Unknown": "<span class='label label-warning'>未知</span>"}, "searchable": {"type": "select", "clearable": true, "options": [{"label": "活跃", "value": "Active"}, {"label": "正在删除", "value": "Terminating"}, {"label": "未知", "value": "Unknown"}]}}, {"name": "pod_count", "label": "容器组数量", "type": "container", "body": [{"type": "link", "body": "<% if (data.metadata.annotations && data.metadata.annotations['pod.count.total']) { %><%= data.metadata.annotations['pod.count.total'] %> <% } else { %><span class='text-gray-300 text-xs'>N/A</span><% } %>", "blank": false, "href": "/#/ns/pod?metadata[namespace]=${metadata.name}"}]}, {"name": "resource", "label": "CPU用量(核)<br><span class='text-gray-500 text-xs'>请求用量/上限用量/实时用量</span>", "type": "container", "width": "150px", "body": [{"type": "tpl", "tpl": "<% if (data.metadata.annotations && data.metadata.annotations['cpu.request']) { %><%= data.metadata.annotations['cpu.request'] %>/<%= data.metadata.annotations['cpu.limit'] %>/<%= data.metadata.annotations['cpu.realtime'] %><% } else { %><span class='text-gray-300 text-xs'>N/A</span><% } %>"}]}, {"name": "resource", "label": "内存用量(Gi)<br><span class='text-gray-500 text-xs'>请求用量/上限用量/实时用量</span>", "type": "container", "width": "150px", "body": [{"type": "tpl", "tpl": "<% if (data.metadata.annotations && data.metadata.annotations['memory.request']) { %><%= data.metadata.annotations['memory.request'] %>/<%= data.metadata.annotations['memory.limit'] %>/<%= data.metadata.annotations['memory.realtime'] %><% } else { %><span class='text-gray-300 text-xs'>N/A</span><% } %>"}]}, {"name": "metadata.labels", "label": "标签", "type": "tpl", "tpl": "${metadata.labels ? '<i class=\"fa fa-tags text-primary\"></i>' : '<i class=\"fa fa-tags text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 标签 (ESC 关闭)", "name": "dialog_labels", "size": "lg", "closeOnEsc": true, "closeOnOutside": true, "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_labels/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "labels", "draggable": false, "value": "${metadata.labels}"}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "metadata.annotations", "label": "注解", "type": "tpl", "tpl": "${metadata.annotations|filterAnnotations|showAnnotationIcon|isTrue:'<i class=\"fa fa-note-sticky text-primary\"></i>':'<i class=\"fa fa-note-sticky text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 注解 (ESC 关闭)", "name": "dialog_annotations", "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_annotations/ns/$metadata.namespace/name/$metadata.name", "initApi": "get:/k8s/$kind/group/$group/version/$version/annotations/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "annotations", "draggable": false, "value": "${annotations}"}]}], "size": "lg", "closeOnEsc": true, "closeOnOutside": true}}]}}, "style": {"cursor": "pointer"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}