{"type": "page", "body": [{"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "button", "icon": "fas fa-plus text-primary", "actionType": "drawer", "label": "新增仓库", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "新增Helm仓库  (ESC 关闭)", "body": {"type": "form", "api": "post:/admin/helm/repo/save", "body": [{"type": "static", "label": "说明", "tpl": "<div class='alert alert-info'><p>添加Helm仓库后，您可以浏览和安装该仓库中的Chart包。如果仓库需要认证，请填写用户名和密码。</p></div>"}, {"type": "input-text", "name": "name", "label": "仓库名称", "required": true, "placeholder": "请输入仓库名称"}, {"type": "input-url", "name": "url", "label": "仓库URL", "required": true, "placeholder": "请输入仓库URL，例如：https://charts.bitnami.com/bitnami", "addOn": {"type": "button", "label": "常用仓库", "icon": "fa fa-list", "actionType": "dialog", "dialog": {"title": "选择常用Helm仓库", "body": {"type": "table", "columns": [{"name": "name", "label": "仓库名称"}, {"name": "url", "label": "仓库URL"}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "选择", "actionType": "custom", "onEvent": {"click": {"actions": [{"actionType": "setValue", "componentName": "name", "args": {"value": "${name}"}}, {"actionType": "setValue", "componentName": "url", "args": {"value": "${url}"}}, {"actionType": "closeDialog"}]}}}]}], "data": [{"name": "Bit<PERSON><PERSON>", "url": "https://charts.bitnami.com/bitnami"}, {"name": "Stable", "url": "https://charts.helm.sh/stable"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://kubernetes.oss-cn-hangzhou.aliyuncs.com/charts"}, {"name": "Incubator", "url": "https://charts.helm.sh/incubator"}, {"name": "Jetstack", "url": "https://charts.jetstack.io"}, {"name": "Prometheus", "url": "https://prometheus-community.github.io/helm-charts"}, {"name": "<PERSON><PERSON>", "url": "https://grafana.github.io/helm-charts"}, {"name": "Elastic", "url": "https://helm.elastic.co"}, {"name": "HashiCorp", "url": "https://helm.releases.hashicorp.com"}, {"name": "Kong", "url": "https://charts.konghq.com"}, {"name": "Traefik", "url": "https://helm.traefik.io/traefik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://istio-release.storage.googleapis.com/charts"}, {"name": "Argo", "url": "https://argoproj.github.io/argo-helm"}, {"name": "Gitlab", "url": "https://charts.gitlab.io"}, {"name": "<PERSON>", "url": "https://charts.jenkins.io"}, {"name": "Kubernetes Dashboard", "url": "https://kubernetes.github.io/dashboard"}, {"name": "Rancher", "url": "https://releases.rancher.com/server-charts/stable"}, {"name": "<PERSON><PERSON>", "url": "https://charts.longhorn.io"}, {"name": "Cilium", "url": "https://helm.cilium.io"}, {"name": "Calico", "url": "https://docs.projectcalico.org/charts"}, {"name": "<PERSON><PERSON>", "url": "https://helm.linkerd.io/stable"}, {"name": "Falco", "url": "https://falcosecurity.github.io/charts"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://jaegertracing.github.io/helm-charts"}, {"name": "<PERSON><PERSON>", "url": "https://kiali.org/helm-charts"}, {"name": "<PERSON>", "url": "https://grafana.github.io/loki/charts"}, {"name": "Minio", "url": "https://helm.min.io"}, {"name": "Rook", "url": "https://charts.rook.io/release"}, {"name": "<PERSON><PERSON>", "url": "https://charts.ceph.com"}, {"name": "Harbor", "url": "https://helm.goharbor.io"}, {"name": "Cert Manager", "url": "https://charts.jetstack.io"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://vmware-tanzu.github.io/helm-charts"}, {"name": "Fluentd", "url": "https://fluent.github.io/helm-charts"}]}}}}, {"type": "input-text", "name": "username", "label": "用户名", "placeholder": "如果仓库需要认证，请输入用户名"}, {"type": "input-password", "name": "password", "label": "密码", "placeholder": "如果仓库需要认证，请输入密码"}, {"type": "input-text", "name": "certFile", "label": "证书文件", "placeholder": "可选：证书文件路径"}, {"type": "input-text", "name": "keyFile", "label": "密钥文件", "placeholder": "可选：密钥文件路径"}, {"type": "input-text", "name": "caFile", "label": "CA证书", "placeholder": "可选：CA证书文件路径"}, {"type": "switch", "name": "insecure_skip_tls_verify", "label": "跳过TLS验证", "onText": "是", "offText": "否"}, {"type": "switch", "name": "pass_credentials_all", "label": "传递所有凭证", "onText": "是", "offText": "否"}], "submitText": "保存", "resetText": "重置", "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失败"}, "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "detailCRUD"}, {"actionType": "closeDrawer"}]}}}}}, {"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, "reload", "bulkActions"], "bulkActions": [{"type": "button", "icon": "fas fa-trash text-danger", "actionType": "ajax", "tooltip": "删除仓库", "confirmText": "确定要删除该仓库吗？", "api": "post:/admin/helm/repo/delete/${ids}"}, {"label": "更新索引", "actionType": "ajax", "confirmText": "确定要更新索引?更新操作为后台操作，请稍后查看操作结果。", "api": {"url": "/admin/helm/repo/update_index", "method": "post", "data": {"ids": "${ids}"}}}], "loadDataOnce": false, "syncLocation": false, "initFetch": true, "perPage": 10, "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "api": "get:/admin/helm/repo/list", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-edit text-primary", "actionType": "drawer", "tooltip": "编辑仓库", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "编辑Helm仓库  (ESC 关闭)", "body": {"type": "form", "api": "post:/admin/helm/repo/save", "body": [{"type": "hidden", "name": "id"}, {"type": "input-text", "name": "name", "label": "仓库名称", "required": true, "placeholder": "请输入仓库名称"}, {"type": "input-url", "name": "url", "label": "仓库URL", "required": true, "placeholder": "请输入仓库URL，例如：https://charts.bitnami.com/bitnami", "addOn": {"type": "button", "label": "常用仓库", "icon": "fa fa-list", "actionType": "dialog", "dialog": {"title": "选择常用Helm仓库", "body": {"type": "table", "columns": [{"name": "name", "label": "仓库名称"}, {"name": "url", "label": "仓库URL"}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "选择", "actionType": "custom", "onEvent": {"click": {"actions": [{"actionType": "setValue", "componentName": "name", "args": {"value": "${name}"}}, {"actionType": "setValue", "componentName": "url", "args": {"value": "${url}"}}, {"actionType": "closeDialog"}]}}}]}], "data": [{"name": "Bit<PERSON><PERSON>", "url": "https://charts.bitnami.com/bitnami"}, {"name": "Stable", "url": "https://charts.helm.sh/stable"}, {"name": "Incubator", "url": "https://charts.helm.sh/incubator"}, {"name": "Jetstack", "url": "https://charts.jetstack.io"}, {"name": "Prometheus", "url": "https://prometheus-community.github.io/helm-charts"}, {"name": "<PERSON><PERSON>", "url": "https://grafana.github.io/helm-charts"}, {"name": "Elastic", "url": "https://helm.elastic.co"}, {"name": "HashiCorp", "url": "https://helm.releases.hashicorp.com"}, {"name": "Kong", "url": "https://charts.konghq.com"}, {"name": "Traefik", "url": "https://helm.traefik.io/traefik"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://istio-release.storage.googleapis.com/charts"}, {"name": "Argo", "url": "https://argoproj.github.io/argo-helm"}, {"name": "Gitlab", "url": "https://charts.gitlab.io"}, {"name": "<PERSON>", "url": "https://charts.jenkins.io"}, {"name": "Kubernetes Dashboard", "url": "https://kubernetes.github.io/dashboard"}, {"name": "Rancher", "url": "https://releases.rancher.com/server-charts/stable"}, {"name": "<PERSON><PERSON>", "url": "https://charts.longhorn.io"}, {"name": "Cilium", "url": "https://helm.cilium.io"}, {"name": "Calico", "url": "https://docs.projectcalico.org/charts"}, {"name": "<PERSON><PERSON>", "url": "https://helm.linkerd.io/stable"}, {"name": "Falco", "url": "https://falcosecurity.github.io/charts"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://jaegertracing.github.io/helm-charts"}, {"name": "<PERSON><PERSON>", "url": "https://kiali.org/helm-charts"}, {"name": "<PERSON>", "url": "https://grafana.github.io/loki/charts"}, {"name": "Minio", "url": "https://helm.min.io"}, {"name": "Rook", "url": "https://charts.rook.io/release"}, {"name": "<PERSON><PERSON>", "url": "https://charts.ceph.com"}, {"name": "Harbor", "url": "https://helm.goharbor.io"}, {"name": "Cert Manager", "url": "https://charts.jetstack.io"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://vmware-tanzu.github.io/helm-charts"}, {"name": "Fluentd", "url": "https://fluent.github.io/helm-charts"}, {"name": "Kyverno", "url": "https://kyverno.github.io/kyverno"}, {"name": "OPA Gatekeeper", "url": "https://open-policy-agent.github.io/gatekeeper/charts"}, {"name": "Vector", "url": "https://helm.vector.dev"}, {"name": "OpenTelemetry", "url": "https://open-telemetry.github.io/opentelemetry-helm-charts"}, {"name": "Sysdig", "url": "https://charts.sysdig.com"}, {"name": "<PERSON><PERSON>", "url": "https://thanos-io.github.io/thanos"}, {"name": "KubeVela", "url": "https://kubevela.net/charts"}, {"name": "OpenEBS", "url": "https://openebs.github.io/charts"}, {"name": "Kubeflow", "url": "https://charts.kubeflow.org"}, {"name": "Keptn", "url": "https://charts.keptn.sh"}, {"name": "Zookeeper", "url": "https://charts.pravega.io"}, {"name": "Kafka", "url": "https://strimzi.io/charts"}, {"name": "NATS", "url": "https://nats-io.github.io/k8s/helm/charts"}, {"name": "PostgreSQL Operator", "url": "https://opensource.zalando.com/postgres-operator/charts/postgres-operator"}, {"name": "MySQL Operator", "url": "https://mysql-operator.github.io/mysql-operator"}, {"name": "MongoDB", "url": "https://mongodb.github.io/helm-charts"}, {"name": "Redis Operator", "url": "https://ot-container-kit.github.io/helm-charts"}, {"name": "OpenSearch", "url": "https://opensearch-project.github.io/helm-charts"}, {"name": "VictoriaMetrics", "url": "https://victoriametrics.github.io/helm-charts"}, {"name": "Tempo", "url": "https://grafana.github.io/helm-charts"}, {"name": "YugabyteDB", "url": "https://charts.yugabyte.com"}, {"name": "Cloud Native PG", "url": "https://cloudnative-pg.github.io/charts"}]}}}}, {"type": "input-text", "name": "username", "label": "用户名", "placeholder": "如果仓库需要认证，请输入用户名"}, {"type": "input-password", "name": "password", "label": "密码", "placeholder": "如果仓库需要认证，请输入密码"}, {"type": "input-text", "name": "certFile", "label": "证书文件", "placeholder": "可选：证书文件路径"}, {"type": "input-text", "name": "keyFile", "label": "密钥文件", "placeholder": "可选：密钥文件路径"}, {"type": "input-text", "name": "caFile", "label": "CA证书", "placeholder": "可选：CA证书文件路径"}, {"type": "switch", "name": "insecure_skip_tls_verify", "label": "跳过TLS验证", "onText": "是", "offText": "否"}, {"type": "switch", "name": "pass_credentials_all", "label": "传递所有凭证", "onText": "是", "offText": "否"}], "submitText": "保存", "resetText": "重置", "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失败"}, "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "detailCRUD"}, {"actionType": "closeDrawer"}]}}}}}], "toggled": true}, {"name": "name", "label": "仓库名称", "type": "text", "width": "180px", "sortable": true, "searchable": {"type": "input-text", "name": "name", "clearable": true, "label": "仓库名称", "placeholder": "输入仓库名称"}}, {"name": "url", "label": "仓库URL", "type": "text", "width": "300px"}, {"name": "generated", "label": "索引时间", "type": "datetime"}, {"name": "updated_at", "label": "更新时间", "type": "datetime"}, {"name": "created_at", "label": "创建时间", "type": "datetime"}]}]}