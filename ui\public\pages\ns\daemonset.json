{"type": "page", "data": {"ns": "${ls:selectedNs||'default'}", "kind": "DaemonSet", "version": "v1", "group": "apps"}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}, {"type": "button", "label": "属性文档", "level": "link", "icon": "fas fa-book-open text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 属性文档（ESC 关闭）", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/kind/$kind/group/$group/version/$version", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}}, {"type": "button", "label": "指南", "level": "link", "icon": "fas fa-lightbulb text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}"}}]}}, {"label": "创建", "icon": "fas fa-dharmachakra text-primary", "type": "button", "level": "link", "actionType": "url", "blank": true, "url": "/#/apply/apply?kind=${kind}"}]}, {"type": "container", "className": "floating-toolbar-right", "body": [{"type": "wrapper", "body": [{"type": "form", "mode": "inline", "wrapWithPanel": false, "body": [{"label": "命名空间", "type": "select", "multiple": true, "maxTagCount": 1, "name": "ns", "id": "ns", "searchable": true, "checkAll": true, "source": "/k8s/ns/option_list", "value": "${ls:selectedNs||'default'}", "onEvent": {"change": {"actions": [{"actionType": "reload", "componentId": "detailCRUD", "data": {"ns": "${ns}"}}, {"actionType": "custom", "script": "localStorage.setItem('selectedNs', event.data.ns)"}]}}}, {"label": "集群", "type": "select", "multiple": false, "name": "cluster", "id": "cluster", "searchable": true, "source": "/params/cluster/option_list", "value": "${ls:cluster}", "onEvent": {"change": {"actions": [{"actionType": "custom", "script": "localStorage.setItem('cluster', event.data.value)"}, {"actionType": "custom", "script": "window.location.reload();"}]}}}]}]}]}, {"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload", "bulkActions"], "loadDataOnce": false, "syncLocation": false, "initFetch": true, "perPage": 10, "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/batch/remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"label": "强制删除", "actionType": "ajax", "confirmText": "确定要批量强制删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/force_remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"type": "button", "actionType": "ajax", "label": "重启", "confirmText": "确定要批量重启?", "api": {"url": "/k8s/daemonset/batch/restart", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"type": "button", "actionType": "ajax", "label": "停止", "confirmText": "确定要批量停止?", "api": {"url": "/k8s/daemonset/batch/stop", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"type": "button", "actionType": "ajax", "label": "恢复", "confirmText": "确定要批量恢复?", "api": {"url": "/k8s/daemonset/batch/restore", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "api": "post:/k8s/$kind/group/$group/version/$version/list/ns/${ns}", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/$kind/group/$group/version/$version/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "name": "${metadata.name}", "namespace": "${metadata.namespace}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group/${group}/version/${version}/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}]}], "actions": []}}, {"type": "dropdown-button", "level": "link", "buttons": [{"type": "button", "icon": "fas fa-wrench text-primary", "label": "更新镜像", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "position": "left", "title": "编辑", "body": [{"type": "page", "body": [{"type": "panel", "title": "当前运行镜像", "body": {"name": "spec.template.spec.containers", "type": "each", "items": {"type": "tpl", "tpl": "<div style='margin-bottom: 10px;'>容器：<strong class='text-green-500 font-black'>${name}</strong><br>镜像： ${image|simpleImageName}</div>"}}}, {"type": "form", "title": "更新镜像", "name": "sample-edit-form", "api": "post:/k8s/$kind/group/$group/version/$version/update_image/ns/$metadata.namespace/name/$metadata.name", "actions": [], "body": [{"label": "镜像拉取密钥", "type": "select", "multiple": true, "maxTagCount": 1, "name": "image_pull_secrets", "id": "image_pull_secrets", "searchable": true, "clearable": true, "noResultsText": "未设置密钥", "checkAll": true, "source": "/k8s/$kind/group/$group/version/$version/image_pull_secrets/ns/$metadata.namespace/name/$metadata.name"}, {"type": "select", "name": "container_name", "label": "容器", "source": "${spec.template.spec.containers | pick:name | map: {label: item, value: item}}", "value": "${spec.template.spec.containers[0].name}", "required": true}, {"type": "service", "api": "/k8s/$kind/group/$group/version/$version/container_info/ns/$metadata.namespace/name/$metadata.name/container/${container_name}", "body": [{"type": "input-text", "name": "image", "label": "镜像名称", "required": true}, {"type": "input-text", "name": "tag", "label": "版本标签", "required": true}, {"type": "select", "name": "image_pull_policy", "label": "镜像拉取策略", "required": true, "options": [{"label": "始终拉取", "value": "Always"}, {"label": "从不拉取", "value": "Never"}, {"label": "不存在时拉取", "value": "IfNotPresent"}]}]}]}]}]}}, {"type": "button", "icon": "fas fa-memory text-primary", "label": "调整资源限制", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "position": "left", "title": "编辑资源限制", "body": [{"type": "page", "body": [{"type": "panel", "title": "当前容器资源", "body": {"name": "spec.template.spec.containers", "type": "each", "items": {"type": "tpl", "tpl": "<div style='margin-bottom: 10px;'>容器：<strong class='text-green-500 font-black'>${name}</strong><br>CPU请求/限制：${resources.requests.cpu|default:'未设置'}/${resources.limits.cpu|default:'未设置'}<br>内存请求/限制：${resources.requests.memory|default:'未设置'}/${resources.limits.memory|default:'未设置'}</div>"}}}, {"type": "form", "title": "更新资源限制", "name": "resource-edit-form", "api": "post:/k8s/$kind/group/$group/version/$version/update_resources/ns/$metadata.namespace/name/$metadata.name", "actions": [], "body": [{"type": "select", "name": "container_name", "label": "容器", "source": "${spec.template.spec.containers | pick:name | map: {label: item, value: item}}", "value": "${spec.template.spec.containers[0].name}", "required": true}, {"type": "service", "api": "/k8s/$kind/group/$group/version/$version/container_resources_info/ns/$metadata.namespace/name/$metadata.name/container/${container_name}", "body": [{"type": "group", "body": [{"type": "panel", "title": "CPU设置", "body": [{"type": "input-text", "name": "request_cpu", "label": "CPU请求", "placeholder": "如: 500m 或 0.5", "description": "容器启动时请求的CPU资源"}, {"type": "input-text", "name": "limit_cpu", "label": "CPU限制", "placeholder": "如: 1 或 1000m", "description": "容器最大可使用的CPU资源"}]}, {"type": "panel", "title": "内存设置", "body": [{"type": "input-text", "name": "request_memory", "label": "内存请求", "placeholder": "如: 512Mi 或 1Gi", "description": "容器启动时请求的内存资源"}, {"type": "input-text", "name": "limit_memory", "label": "内存限制", "placeholder": "如: 1Gi 或 2Gi", "description": "容器最大可使用的内存资源"}]}]}]}]}]}]}}, {"type": "button", "icon": "fas fa-check-circle text-primary", "label": "健康检查配置", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": false, "size": "lg", "position": "left", "title": "健康检查配置", "body": {"type": "form", "title": "健康检查配置", "name": "health-check-form", "api": {"method": "post", "url": "/k8s/$kind/group/$group/version/$version/update_health_checks/ns/$metadata.namespace/name/$metadata.name", "data": {"container_name": "${container_name}", "liveness_type": "${liveness_probe.type}", "readiness_type": "${readiness_probe.type}", "liveness_probe": "${liveness_probe.type === null ? {} : (liveness_probe.type ? { httpGet: liveness_probe.type == 'httpGet' ? {path: liveness_probe.httpGet.path, port: liveness_probe.httpGet.port, scheme: liveness_probe.httpGet.scheme, httpHeaders: liveness_probe.httpGet.httpHeaders} : undefined, tcpSocket: liveness_probe.type == 'tcpSocket' ? {port: liveness_probe.tcpSocket.port} : undefined, exec: liveness_probe.type == 'exec' ? {command: liveness_probe.exec.command} : undefined, initialDelaySeconds: liveness_probe.initialDelaySeconds, periodSeconds: liveness_probe.periodSeconds, timeoutSeconds: liveness_probe.timeoutSeconds, failureThreshold: liveness_probe.failureThreshold, successThreshold: liveness_probe.successThreshold } : {})}", "readiness_probe": "${readiness_probe.type === null ? {} : (readiness_probe.type ? { httpGet: readiness_probe.type == 'httpGet' ? {path: readiness_probe.httpGet.path, port: readiness_probe.httpGet.port, scheme: readiness_probe.httpGet.scheme, httpHeaders: readiness_probe.httpGet.httpHeaders} : undefined, tcpSocket: readiness_probe.type == 'tcpSocket' ? {port: readiness_probe.tcpSocket.port} : undefined, exec: readiness_probe.type == 'exec' ? {command: readiness_probe.exec.command} : undefined, initialDelaySeconds: readiness_probe.initialDelaySeconds, periodSeconds: readiness_probe.periodSeconds, timeoutSeconds: readiness_probe.timeoutSeconds, failureThreshold: readiness_probe.failureThreshold, successThreshold: readiness_probe.successThreshold } : {})}"}}, "body": [{"type": "select", "name": "container_name", "label": "容器", "source": "${spec.template.spec.containers | pick:name | map: {label: item, value: item}}", "value": "${spec.template.spec.containers[0].name}", "required": true}, {"type": "service", "api": "/k8s/$kind/group/$group/version/$version/container_health_checks/ns/$metadata.namespace/name/$metadata.name/container/${container_name}", "adaptor": "return {\n  status: payload.status,\n  data: payload.data || {\n    container_name: '',\n    liveness_probe: null,\n    readiness_probe: null\n  }\n};", "body": [{"type": "tabs", "tabs": [{"title": "存活探针", "body": [{"type": "select", "name": "liveness_probe.type", "label": "探针类型", "value": "${liveness_probe ? (liveness_probe.httpGet ? 'httpGet' : liveness_probe.tcpSocket ? 'tcpSocket' : liveness_probe.exec ? 'exec' : null) : null}", "options": [{"label": "HTTP请求", "value": "httpGet"}, {"label": "TCP端口检查", "value": "tcpSocket"}, {"label": "执行命令", "value": "exec"}, {"label": "不设置", "value": null}], "mode": "inline", "clearable": true}, {"type": "container", "visibleOn": "${liveness_probe && liveness_probe.type !== null }", "body": [{"type": "fieldSet", "title": "HTTP请求设置", "visibleOn": "${liveness_probe.type == 'httpGet'}", "body": [{"type": "input-text", "name": "liveness_probe.httpGet.path", "label": "HTTP路径", "value": "${liveness_probe?.httpGet?.path || ''}", "placeholder": "健康检查路径，如：/healthz", "required": true}, {"type": "input-number", "name": "liveness_probe.httpGet.port", "label": "端口", "value": "${liveness_probe?.httpGet?.port || ''}", "placeholder": "健康检查端口号", "required": true, "min": 1, "max": 65535}, {"type": "select", "name": "liveness_probe.httpGet.scheme", "label": "协议", "value": "${liveness_probe?.httpGet?.scheme || 'HTTP'}", "required": true, "options": [{"label": "HTTP", "value": "HTTP"}, {"label": "HTTPS", "value": "HTTPS"}]}, {"type": "combo", "name": "liveness_probe.httpGet.httpHeaders", "label": "HTTP头信息", "multiple": true, "addable": true, "removable": true, "items": [{"type": "input-text", "name": "name", "label": "头名称", "placeholder": "如: Authorization"}, {"type": "input-text", "name": "value", "label": "头值", "placeholder": "如: Bearer tokenxxxxxxxx"}], "value": "${liveness_probe?.httpGet?.httpHeaders || []}", "description": "可选的自定义HTTP头信息"}]}, {"type": "fieldSet", "title": "TCP端口检查设置", "visibleOn": "${liveness_probe.type == 'tcpSocket'}", "body": [{"type": "input-number", "name": "liveness_probe.tcpSocket.port", "label": "TCP端口", "value": "${liveness_probe?.tcpSocket?.port || ''}", "placeholder": "需要检查的TCP端口号", "required": true, "min": 1, "max": 65535}]}, {"type": "fieldSet", "title": "执行命令设置", "visibleOn": "${liveness_probe.type == 'exec'}", "body": [{"type": "input-array", "name": "liveness_probe.exec.command", "label": "执行命令", "items": {"type": "input-text", "placeholder": "请输入命令"}, "value": "${liveness_probe?.exec?.command?.length > 0 ? liveness_probe.exec.command : ['/bin/bash', '-c', 'ps -ef']}", "pipeIn": "return Array.isArray(value) ? value : (value ? [value] : ['/bin/bash', '-c', ''])", "addButtonText": "添加参数"}]}, {"type": "fieldSet", "title": "通用参数设置", "body": [{"type": "input-number", "name": "liveness_probe.initialDelaySeconds", "label": "初始延迟秒数", "value": "${liveness_probe?.initialDelaySeconds || 30}", "min": 1, "placeholder": "容器启动后等待多少秒开始探测", "required": true}, {"type": "input-number", "name": "liveness_probe.periodSeconds", "label": "检测周期秒数", "value": "${liveness_probe?.periodSeconds || 10}", "min": 1, "placeholder": "每次探测间隔多少秒", "required": true}, {"type": "input-number", "name": "liveness_probe.timeoutSeconds", "label": "超时秒数", "value": "${liveness_probe?.timeoutSeconds || 5}", "min": 1, "placeholder": "探测超时时间（秒）", "required": true}, {"type": "input-number", "name": "liveness_probe.failureThreshold", "label": "失败阈值", "value": "${liveness_probe?.failureThreshold || 3}", "min": 1, "required": true, "placeholder": "连续失败多少次标记为不健康"}, {"type": "input-number", "name": "liveness_probe.successThreshold", "label": "成功阈值", "value": "${liveness_probe?.successThreshold || 1}", "required": true, "min": 1, "max": 1, "placeholder": "连续成功多少次标记为健康"}]}]}]}, {"title": "就绪探针", "body": [{"type": "select", "name": "readiness_probe.type", "label": "探针类型", "value": "${readiness_probe ? (readiness_probe.httpGet ? 'httpGet' : readiness_probe.tcpSocket ? 'tcpSocket' : readiness_probe.exec ? 'exec' : null) : null}", "options": [{"label": "HTTP请求", "value": "httpGet"}, {"label": "TCP端口检查", "value": "tcpSocket"}, {"label": "执行命令", "value": "exec"}, {"label": "不设置", "value": null}], "mode": "inline", "clearable": true}, {"type": "container", "visibleOn": "${readiness_probe && readiness_probe.type !== null }", "body": [{"type": "fieldSet", "title": "HTTP请求设置", "visibleOn": "${readiness_probe.type == 'httpGet'}", "body": [{"type": "input-text", "name": "readiness_probe.httpGet.path", "label": "HTTP路径", "value": "${readiness_probe?.httpGet?.path || ''}", "placeholder": "就绪检查路径，如：/ready", "required": true}, {"type": "input-number", "name": "readiness_probe.httpGet.port", "label": "端口", "value": "${readiness_probe?.httpGet?.port || ''}", "placeholder": "就绪检查端口号", "required": true, "min": 1, "max": 65535}, {"type": "select", "name": "readiness_probe.httpGet.scheme", "label": "协议", "value": "${readiness_probe?.httpGet?.scheme || 'HTTP'}", "required": true, "options": [{"label": "HTTP", "value": "HTTP"}, {"label": "HTTPS", "value": "HTTPS"}]}, {"type": "combo", "name": "liveness_probe.httpGet.httpHeaders", "label": "HTTP头信息", "multiple": true, "addable": true, "removable": true, "items": [{"type": "input-text", "name": "name", "label": "头名称", "placeholder": "如: Authorization"}, {"type": "input-text", "name": "value", "label": "头值", "placeholder": "如: Bearer tokenxxxxxxxx"}], "value": "${liveness_probe?.httpGet?.httpHeaders || []}", "description": "可选的自定义HTTP头信息"}]}, {"type": "fieldSet", "title": "TCP端口检查设置", "visibleOn": "${readiness_probe.type == 'tcpSocket'}", "body": [{"type": "input-number", "name": "readiness_probe.tcpSocket.port", "label": "TCP端口", "value": "${readiness_probe?.tcpSocket?.port || ''}", "placeholder": "需要检查的TCP端口号", "required": true, "min": 1, "max": 65535}]}, {"type": "fieldSet", "title": "执行命令设置", "visibleOn": "${readiness_probe.type == 'exec'}", "body": [{"type": "input-array", "name": "readiness_probe.exec.command", "label": "执行命令", "items": {"type": "input-text", "placeholder": "请输入命令"}, "value": "${readiness_probe?.exec?.command?.length > 0 ? readiness_probe.exec.command : ['/bin/bash', '-c', 'ps -ef']}", "pipeIn": "return Array.isArray(value) ? value : (value ? [value] : ['/bin/bash', '-c', ''])", "addButtonText": "添加参数"}]}, {"type": "fieldSet", "title": "通用参数设置", "body": [{"type": "input-number", "name": "readiness_probe.initialDelaySeconds", "label": "初始延迟秒数", "value": "${readiness_probe?.initialDelaySeconds || 30}", "required": true, "min": 1, "placeholder": "容器启动后等待多少秒开始探测"}, {"type": "input-number", "name": "readiness_probe.periodSeconds", "label": "检测周期秒数", "value": "${readiness_probe?.periodSeconds || 10}", "required": true, "min": 1, "placeholder": "每次探测间隔多少秒"}, {"type": "input-number", "name": "readiness_probe.timeoutSeconds", "label": "超时秒数", "value": "${readiness_probe?.timeoutSeconds || 5}", "min": 1, "placeholder": "探测超时时间（秒）"}, {"type": "input-number", "name": "readiness_probe.failureThreshold", "label": "失败阈值", "value": "${readiness_probe?.failureThreshold || 3}", "required": true, "min": 1, "placeholder": "连续失败多少次标记为不健康"}, {"type": "input-number", "name": "readiness_probe.successThreshold", "label": "成功阈值", "value": "${readiness_probe?.successThreshold || 1}", "required": true, "min": 1, "placeholder": "连续成功多少次标记为健康"}]}]}]}]}]}]}}}, {"type": "button", "icon": "fas fa-list text-primary", "label": "设置环境变量", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "position": "left", "size": "lg", "title": "设置环境变量", "body": [{"type": "page", "body": [{"type": "form", "title": "设置环境变量", "name": "resource-edit-form", "api": "post:/k8s/$kind/group/$group/version/$version/update_env/ns/$metadata.namespace/name/$metadata.name", "actions": [], "body": [{"type": "select", "name": "container_name", "label": "容器", "source": "${spec.template.spec.containers | pick:name | map: {label: item, value: item}}", "value": "${spec.template.spec.containers[0].name}", "required": true}, {"type": "service", "api": "/k8s/$kind/group/$group/version/$version/container_env/ns/$metadata.namespace/name/$metadata.name/container/${container_name}", "body": [{"type": "input-kv", "name": "envs", "draggable": false, "value": "${envs}", "valueSchema": {"type": "textarea", "minRows": 1}}]}]}]}]}}]}], "toggled": true}, {"name": "metadata.namespace", "label": "命名空间", "type": "text", "sortable": true}, {"name": "metadata.name", "label": "名称", "type": "container", "body": [{"type": "button", "label": "${metadata.name}", "level": "link", "actionType": "drawer", "drawer": {"closeOnEsc": true, "title": "${kind}/${metadata.namespace}/${metadata.name}-${status.podIP} 关联资源 （ESC 关闭）", "size": "xl", "actions": [{"type": "button", "label": "关闭", "close": true}], "body": {"type": "tabs", "tabs": [{"title": "容器组", "body": [{"type": "crud", "id": "detailEvent", "name": "detailEvent", "headerToolbar": ["reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}, "bulkActions"], "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": {"url": "/k8s/Pod/group//version/v1/batch/remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"label": "强制删除", "actionType": "ajax", "confirmText": "确定要批量强制删除?", "api": {"url": "/k8s/Pod/group//version/v1/force_remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}], "loadDataOnce": true, "syncLocation": false, "perPage": 10, "api": "get:/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/pod", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "actionType": "drawer", "icon": "fa fa-edit text-primary", "tooltip": "编辑", "style": {"border": "None"}, "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/Pod/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/Pod/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}]}], "actions": []}}, {"tooltip": "终端", "type": "button", "level": "link", "actionType": "url", "icon": "fa fa-terminal text-primary", "blank": true, "url": "/#/PodExec?namespace=${metadata.namespace}&name=${metadata.name}"}, {"type": "button", "icon": "fas fa-binoculars text-primary", "actionType": "drawer", "tooltip": "端口", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "${metadata.name} 端口 (ESC 关闭)", "body": [{"type": "crud", "id": "detailPortList", "name": "detailPortList", "headerToolbar": ["reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "loadDataOnce": true, "syncLocation": false, "perPage": 10, "api": "get:/k8s/pod/port_forward/ns/$metadata.namespace/name/$metadata.name/port/list", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "icon": "fas fa-random text-primary", "label": "转发", "actionType": "dialog", "dialog": {"title": "端口转发", "body": [{"type": "form", "api": "post:/k8s/pod/port_forward/ns/$metadata.namespace/name/$metadata.name/container/${container_name}/pod_port/${pod_port}/local_port/${local_port}/start", "body": [{"type": "switch", "name": "specify_port", "label": "指定端口"}, {"type": "input-number", "name": "local_port", "label": "转发端口", "visibleOn": "this.specify_port == true", "min": 1, "max": 65535}], "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "detailPortList"}, {"actionType": "closeDrawer"}]}}}]}}, {"type": "button", "actionType": "ajax", "icon": "fas fa-stop text-primary", "label": "关闭", "api": "post:/k8s/pod/port_forward/ns/$metadata.namespace/name/$metadata.name/container/${container_name}/pod_port/${pod_port}/stop", "onEvent": {"success": {"actions": [{"actionType": "reload", "componentId": "detailPortList"}]}}}], "toggled": true}, {"name": "container_name", "label": "容器名称", "type": "text"}, {"name": "pod_port", "label": "Pod端口", "type": "tpl", "tpl": "${port_name ? port_name +':': ''}${pod_port ? pod_port : ''}${protocol ?'['+ protocol+']' : ''}"}, {"name": "local_port", "label": "访问端口", "type": "tpl", "tpl": "${local_port ? `${local_port} <a href='http://127.0.0.1:${local_port}' target='_blank'>http</a> <a href='https://127.0.0.1:${local_port}' target='_blank'>https</a>` : ''}"}, {"name": "status", "label": "状态", "type": "text"}]}]}}], "toggled": true}, {"name": "metadata.namespace", "label": "命名空间", "type": "text"}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px"}, {"name": "ready", "label": "就绪", "type": "k8sPodReady"}, {"name": "status.phase", "label": "状态", "type": "k8sPodStatus"}, {"name": "status.podIP", "label": "容器组IP", "type": "text"}, {"name": "spec.containers", "label": "容器镜像<br><span class='text-gray-500 text-xs'>容器名称=>镜像:版本</span>", "width": "150px", "type": "each", "items": {"type": "tpl", "tpl": "<div style='margin-bottom: 10px;'><strong class='text-green-500 font-black'>${name}</strong>=>${image|simpleImageName}</div>"}}, {"name": "status.containerStatuses", "label": "重启次数", "type": "tpl", "tpl": "${status.containerStatuses|pick:restartCount | map  | sum }"}, {"name": "spec.nodeName", "label": "所在节点", "type": "text"}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}, {"title": "事件", "body": [{"type": "crud", "id": "detailEvent", "name": "detailEvent", "headerToolbar": ["reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "loadDataOnce": true, "syncLocation": false, "perPage": 10, "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name/event", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "icon": "fas fa-brain text-primary", "label": "AI问诊", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI 查询", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/event", "params": {"note": "${note}", "source": "${source}", "reason": "${reason}", "reportingController": "${reportingController}", "type": "${type}", "regardingKind": "${kind}"}}]}, "visibleOn": "type === 'Warning'"}], "toggled": true}, {"name": "type", "label": "类型", "filterable": {"options": [{"label": "正常 ", "value": "Normal"}, {"label": "告警 ", "value": "Warning"}]}, "type": "mapping", "map": {"Normal": "<span class='label label-success'>正常</span>", "Warning": "<span class='label label-danger'>告警</span>"}}, {"name": "reason", "label": "原因", "type": "text"}, {"name": "field", "label": "关联字段", "type": "tpl", "tpl": "${regarding.fieldPath}"}, {"name": "source", "label": "事件来源", "type": "tpl", "tpl": "${reportingController} ${reportingInstance}"}, {"name": "note", "label": "说明", "type": "text", "searchable": true}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}, {"title": "日志", "body": [{"type": "podsLogViewer", "url": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/pod"}]}, {"title": "变更历史", "body": [{"type": "crud", "id": "detailEvent", "name": "detailEvent", "headerToolbar": ["reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "loadDataOnce": false, "syncLocation": false, "perPage": 10, "api": "get:/k8s/daemonset/ns/$metadata.namespace/name/$metadata.name/rollout/history", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "icon": "fas fa-undo-alt text-danger", "actionType": "ajax", "label": "回滚", "confirmText": "您确认要回滚到 $revision ?", "api": "post:/k8s/daemonset/ns/$metadata.namespace/name/$metadata.name/revision/$revision/rollout/undo", "onEvent": {"click": {"actions": [{"actionType": "closeDrawer"}]}}}, {"type": "button", "icon": "fa fa-edit text-primary", "label": "查看", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "查看版本", "body": [{"type": "service", "api": "get:/k8s/${gvk.Kind}/group/${gvk.Group}/version/${gvk.Version}/ns/$namespace/name/$name", "body": [{"type": "editor", "name": "yaml", "size": "xxl", "allowFullscreen": true, "placeholder": "loading", "language": "yaml", "value": "${yaml}", "options": {"wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}}], "toggled": true}, {"name": "revision", "label": "版本", "type": "text"}, {"name": "namespace", "label": "命名空间", "type": "text"}, {"name": "name", "label": "名称", "type": "text"}, {"name": "containers", "label": "容器", "type": "each", "items": {"type": "tpl", "tpl": "<div style='margin-bottom: 10px;'><strong class='text-green-500 font-black'>${name}</strong>: ${image|simpleImageName}</div>"}}, {"name": "creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}, {"title": "网络", "body": [{"type": "tabs", "tabs": [{"title": "Ingress入口", "body": {"type": "crud", "loadDataOnce": true, "headerToolbar": ["reload"], "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/ingress", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "名称", "type": "text"}, {"name": "spec.rules", "label": "访问规则", "type": "tpl", "tpl": "<% if (data.spec.rules && data.spec.rules.length) { %><% data.spec.rules.forEach(function(rule) { %><div style='display: inline-block; margin-right: 20px; padding: 10px;'>域名: <%- rule.host || '*' %><br><% if (rule.http && rule.http.paths && rule.http.paths.length) { %><% rule.http.paths.forEach(function(path) { %><% if (path.backend && path.backend.service) { %>路径: <%- path.path %> → 服务: <%- path.backend.service.name %><br><% } %><% }); %><% } %></div><% }); %><% } %>"}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}, {"title": "Service网络", "body": {"type": "crud", "loadDataOnce": true, "headerToolbar": ["reload"], "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/services", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "Service 名称", "type": "text"}, {"name": "spec.type", "label": "访问类型", "type": "tpl", "tpl": "<% if (data.spec && data.spec.type === 'ClusterIP' && data.spec.clusterIP === 'None') { %><span class='label label-warning'>Headless</span><% } else { %><span class='label label-success'><%= data.spec.type %></span><% } %>"}, {"name": "spec.clusterIP", "label": "访问地址", "type": "text"}, {"name": "spec.ports", "label": "访问端口<br><span class='text-gray-500 text-xs'>访问端口[:节点端口]/协议 => 容器端口</span>", "type": "each", "items": {"type": "tpl", "tpl": "<div>${port}${nodePort?':<span class=\"text text-danger font-black\">'+nodePort+'</span>':''}/${protocol}  <span class=\"text text-success font-black\">=>${targetPort}</span></div>"}}, {"name": "spec.sessionAffinity", "label": "会话亲和性", "type": "mapping", "map": {"ClientIP": "<span class='label label-danger'>ClientIP</span>", "None": "<span class='label label-success'>None</span>"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}, {"title": "端点", "body": {"type": "crud", "loadDataOnce": true, "headerToolbar": ["reload"], "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/endpoints", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "端点 名称", "type": "text"}, {"name": "subsets", "label": "就绪IP", "type": "tpl", "tpl": "<% if (data.subsets && data.subsets[0].addresses) { %><% data.subsets[0].addresses.forEach(function(address) { %><span class='text-green-500 font-black'><%= address.ip %><% if (address.targetRef) { %> 【<%= address.targetRef.namespace %>/<%= address.targetRef.name %>】<% } %></span><br><% }); %><% } %>"}, {"name": "subsets", "label": "未就绪IP", "type": "tpl", "tpl": "<% if (data.subsets && data.subsets[0].notReadyAddresses) { %><% data.subsets[0].notReadyAddresses.forEach(function(address) { %><span class='text-red-500 font-black'><%= address.ip %><% if (address.targetRef) { %> 【<%= address.targetRef.namespace %>/<%= address.targetRef.name %>】<% } %></span><br><% }); %><% } %>"}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}]}]}, {"title": "存储", "body": [{"type": "tabs", "tabs": [{"title": "存储卷声明", "body": {"type": "crud", "loadDataOnce": true, "headerToolbar": ["reload"], "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/pvc", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px", "sortable": true}, {"name": "metadata.annotations.pvcMounts", "label": "挂载信息", "type": "tpl", "tpl": "<% const mounts = (data.metadata.annotations && data.metadata.annotations.pvcMounts) ? JSON.parse(data.metadata.annotations.pvcMounts) : []; %><% if (mounts.length > 0) { %><% mounts.forEach((item) => { %>挂载文件夹: <%= item.mountPath + (item.subPath ? '/' + item.subPath : '') %>,  只读: <%= item.readOnly ? '是' : '否' %><br><% }); %><% } else { %>没有挂载信息<% } %>"}, {"name": "status.phase", "label": "状态", "type": "mapping", "map": {"Bound": "<span class='label label-success'>已绑定</span>", "Lost": "<span class='label label-danger'>丢失</span>", "Pending": "<span class='label label-warning'>未绑定</span>"}}, {"name": "request", "label": "申请容量", "type": "tpl", "tpl": "${spec.resources.requests.storage}"}, {"name": "capacity", "label": "存储容积", "type": "tpl", "tpl": "${status.capacity.storage}"}, {"name": "spec.accessModes", "label": "访问模式", "type": "tpl", "tpl": "<%= data.spec.accessModes.map(item => item === 'ReadWriteOnce' ? '<span class=\\'label label-success\\'>单路读写</span>' : item === 'ReadOnlyMany' ? '<span class=\\'label label-warning\\'>多路只读</span>' : item === 'ReadWriteMany' ? '<span class=\\'label label-info\\'>多路读写</span>' : '').join(' ') %>"}, {"name": "spec.volumeMode", "label": "卷模式", "type": "mapping", "map": {"Filesystem": "<span class='label label-success'>文件系统</span>", "Block": "<span class='label label-warning'>块设备</span>"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}, {"title": "存储卷", "body": {"type": "crud", "loadDataOnce": true, "headerToolbar": ["reload"], "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/pv", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px", "sortable": true}, {"name": "spec.accessModes", "label": "访问模式", "type": "tpl", "tpl": "<%= data.spec.accessModes.map(item => item === 'ReadWriteOnce' ? '<span class=\\'label label-success\\'>单路读写</span>' : item === 'ReadOnlyMany' ? '<span class=\\'label label-warning\\'>多路只读</span>' : item === 'ReadWriteMany' ? '<span class=\\'label label-info\\'>多路读写</span>' : '').join(' ') %>"}, {"name": "spec.capacity.storage", "label": "存储容积", "type": "text"}, {"name": "spec.persistentVolumeReclaimPolicy", "label": "回收策略", "type": "mapping", "map": {"Retain": "<span class='label label-success'>保留</span>", "Recycle": "<span class='label label-info'>回收</span>", "Delete": "<span class='label label-danger'>删除</span>"}}, {"name": "status.phase", "label": "状态", "type": "mapping", "map": {"Available": "<span class='label label-success'>待绑定</span>", "Bound": "<span class='label label-info'>已绑定</span>", "Failed": "<span class='label label-danger'>回收失败</span>", "Pending": "<span class='label label-primary'>待分配</span>", "Released": "<span class='label label-success'>已释放</span>"}}, {"name": "claim", "label": "PVC声明", "type": "tpl", "tpl": "${spec.claimRef.namespace}/${spec.claimRef.name}"}, {"name": "spec.storageClassName", "label": "存储类名称", "type": "text"}, {"name": "spec.volumeMode", "label": "卷模式", "type": "mapping", "map": {"Filesystem": "<span class='label label-success'>文件系统</span>", "Block": "<span class='label label-warning'>块设备</span>"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}]}]}, {"title": "环境变量", "body": {"type": "crud", "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, "reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "loadDataOnce": true, "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/envFromPod", "columns": [{"name": "containerName", "label": "容器", "type": "text", "sortable": true, "searchable": {"type": "select", "name": "containerName", "source": "${spec.containers | pick:name | map: {label: item, value: item}}", "clearable": true}}, {"name": "envName", "label": "环境变量名称", "type": "text", "sortable": true, "searchable": true}, {"name": "envValue", "label": "环境变量值", "type": "text", "sortable": true, "searchable": true}]}}, {"title": "配置映射", "body": {"type": "crud", "loadDataOnce": true, "headerToolbar": ["reload"], "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/configmap", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "名称", "type": "text"}, {"name": "metadata.annotations.configMapMounts", "label": "挂载信息", "type": "tpl", "tpl": "<% const mounts = (data.metadata.annotations && data.metadata.annotations.configMapMounts) ? JSON.parse(data.metadata.annotations.configMapMounts) : []; %><% if (mounts.length > 0) { %><% mounts.forEach((item) => { %>挂载文件夹: <%= item.mountPath + (item.subPath ? '/' + item.subPath : '') %>, 模式: <%= '0' + (item.mode || 0).toString(8) %>, 只读: <%= item.readOnly ? '是' : '否' %><br><% }); %><% } else { %>没有挂载信息<% } %>"}, {"label": "文件列表", "type": "each", "name": "data", "items": {"type": "button", "level": "link", "label": "${key}", "actionType": "drawer", "drawer": {"size": "lg", "closeOnEsc": true, "closeOnOutside": true, "title": "查看 ${metadata.name}-${key} 文件内容 (ESC 关闭)", "body": [{"type": "editor", "name": "yaml", "size": "xxl", "allowFullscreen": true, "placeholder": "loading", "language": "yaml", "value": "${value}", "options": {"wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}, {"title": "密钥", "body": {"loadDataOnce": true, "headerToolbar": ["reload"], "type": "crud", "api": "/k8s/${kind}/group/${group}/version/${version}/ns/${metadata.namespace}/name/${metadata.name}/links/secret", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/${kind}/group//version/v1/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"describe": "${result}", "kind": "${kind}", "group": "${group}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/${kind}/group//version/v1/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group//version/v1/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "toolbar": [{"type": "tpl", "tpl": ""}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}]}]}]}]}], "actions": []}}], "toggled": true}, {"name": "metadata.name", "label": "名称", "type": "text"}, {"name": "type", "label": "类型", "type": "mapping", "map": {"Opaque": "<span class='label label-primary'>自定义数据</span>", "kubernetes.io/service-account-token": "<span class='label label-primary'>服务账户令牌</span>", "kubernetes.io/dockercfg": "<span class='label label-info'>旧版镜像凭证</span>", "kubernetes.io/dockerconfigjson": "<span class='label label-success'>镜像凭证</span>", "kubernetes.io/basic-auth": "<span class='label label-warning'>Basic Auth 凭证</span>", "kubernetes.io/ssh-auth": "<span class='label label-warning'>SSH 凭证</span>", "kubernetes.io/tls": "<span class='label label-danger'>TLS 凭证</span>", "bootstrap.kubernetes.io/token": "<span class='label label-default'>引导令牌</span>", "*": "<span class='label label-primary'>${type}</span>"}}, {"name": "metadata.annotations.secretMounts", "label": "挂载信息", "type": "tpl", "tpl": "<% const mounts = (data.metadata.annotations && data.metadata.annotations.secretMounts) ? JSON.parse(data.metadata.annotations.secretMounts) : []; %><% if (mounts.length > 0) { %><% mounts.forEach((item) => { %>挂载文件夹: <%= item.mountPath + (item.subPath ? '/' + item.subPath : '') %>, 模式: <%= '0' + (item.mode || 0).toString(8) %>, 只读: <%= item.readOnly ? '是' : '否' %><br><% }); %><% } else { %>没有挂载信息<% } %>"}, {"label": "文件列表", "type": "each", "name": "data", "items": {"type": "button", "level": "link", "label": "${key}", "actionType": "drawer", "drawer": {"size": "lg", "closeOnEsc": true, "closeOnOutside": true, "title": "查看 ${metadata.name}-${key} 文件内容 (ESC 关闭)", "body": [{"type": "editor", "name": "yaml", "size": "xxl", "allowFullscreen": true, "placeholder": "loading", "language": "yaml", "value": "${value| base64Decode}", "options": {"wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}}]}}}], "width": "180px", "sortable": true, "searchable": {"type": "input-text", "name": "metadata.name", "clearable": true, "label": "名称", "placeholder": "输入名称"}}, {"name": "metadata.labels", "label": "标签", "type": "tpl", "tpl": "${metadata.labels ? '<i class=\"fa fa-tags text-primary\"></i>' : '<i class=\"fa fa-tags text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 标签 (ESC 关闭)", "name": "dialog_labels", "size": "lg", "closeOnEsc": true, "closeOnOutside": true, "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_labels/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "labels", "draggable": false, "value": "${metadata.labels}"}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "metadata.annotations", "label": "注解", "type": "tpl", "tpl": "${metadata.annotations|filterAnnotations|showAnnotationIcon|isTrue:'<i class=\"fa fa-note-sticky text-primary\"></i>':'<i class=\"fa fa-note-sticky text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 注解 (ESC 关闭)", "name": "dialog_annotations", "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_annotations/ns/$metadata.namespace/name/$metadata.name", "initApi": "get:/k8s/$kind/group/$group/version/$version/annotations/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "annotations", "draggable": false, "value": "${annotations}"}]}], "size": "lg", "closeOnEsc": true, "closeOnOutside": true}}]}}, "style": {"cursor": "pointer"}}, {"name": "spec.template.spec.tolerations", "label": "容忍度", "type": "tpl", "tpl": "${spec.template.spec.tolerations ? '<i class=\"fa-solid fa-exclamation-triangle text-primary\"></i>' : '<i class=\"fa-solid fa-exclamation-triangle text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${metadata.name} 容忍度 (ESC 关闭)", "body": [{"type": "panel", "title": "调度规则：如何使用 容忍度", "body": [{"type": "button", "label": "什么是容忍度", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是容忍度（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是容忍度"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>容忍度规则:</strong> Pod 必须容忍节点上的污点（Taints），才能调度到该节点。容忍度由 key、operator、value 和 effect 组成，其中 key 代表污点键，operator 决定匹配方式，effect 代表污点的影响类型。</div><div><strong>匹配规则:</strong><br>1. key 和 effect 必须与节点上的污点完全一致，否则容忍度无效。<br>2. operator 决定 value 是否必须匹配：当 operator 为 Equal 时，value 也必须与节点污点的 value 一致；当 operator 为 Exists 时，只要 key 存在即可，不关心 value。</div><div><strong>调度行为:</strong> 仅当 Pod 的容忍度与节点污点的 key 和 effect 匹配，并且 value 符合 operator 规则时，Pod 才能调度到该节点，否则调度将被阻止。</div></div>"}]}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_tolerations/ns/$metadata.namespace/name/$metadata.name", "quickSaveItemApi": "/k8s/$kind/group/$group/version/$version/update_tolerations/ns/$metadata.namespace/name/$metadata.name", "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": [{"type": "form", "mode": "horizontal", "id": "toleration_add_form", "api": "/k8s/$kind/group/$group/version/$version/add_tolerations/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-text", "name": "key", "label": "键", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "污点速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择污点（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/taints/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "toleration_add_form", "args": {"value": {"key": "${event.data.item.key}", "value": "${event.data.item.value}", "effect": "${event.data.item.effect}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "effect", "label": "效果", "searchable": {"type": "select", "options": [{"label": "禁止调度", "value": "NoSchedule"}, {"label": "优先不调度", "value": "PreferNoSchedule"}, {"label": "不可执行", "value": "NoExecute"}], "name": "effect", "clearable": true, "placeholder": "输选择效果"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}, {"type": "select", "name": "operator", "label": "操作符", "required": true, "value": "Equal", "options": [{"label": "等于", "value": "Equal"}, {"label": "存在", "value": "Exists"}]}, {"name": "value", "type": "input-text", "label": "值", "visibleOn": "${operator === 'Equal'}"}, {"name": "effect", "label": "效果", "required": true, "value": "NoSchedule", "type": "select", "options": [{"label": "禁止调度", "value": "NoSchedule"}, {"label": "优先不调度", "value": "PreferNoSchedule"}, {"label": "不可执行", "value": "NoExecute"}]}]}]}}], "showIndex": true, "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_tolerations/ns/$metadata.namespace/name/$metadata.name"}], "toggled": true}, {"name": "key", "label": "键"}, {"name": "operator", "label": "操作符", "placeholder": "-", "type": "mapping", "map": {"Equal": "<span class='label label-success'>等于</span>", "Exists": "<span class='label label-success'>存在</span>"}, "quickEdit": {"type": "select", "options": [{"label": "等于", "value": "Equal"}, {"label": "存在", "value": "Exists"}], "saveImmediately": true}}, {"name": "value", "label": "值", "placeholder": "-", "quickEdit": {"type": "input-text", "saveImmediately": true}}, {"name": "effect", "label": "效果", "type": "mapping", "map": {"NoSchedule": "<span class='label label-warning'>禁止调度</span>", "PreferNoSchedule": "<span class='label label-danger'>优先不调度</span>", "NoExecute": "<span class='label label-danger'>不可执行</span>"}}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "spec.template.spec.affinity", "label": "亲和性", "type": "tpl", "tpl": "${spec.template.spec.affinity ? '<i class=\"fa-solid fa-sitemap text-primary\"></i>' : '<i class=\"fa-solid fa-sitemap text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${metadata.name} 亲和性 (ESC 关闭)", "body": [{"type": "tabs", "swipeable": true, "tabs": [{"title": "节点亲和性", "tab": [{"type": "panel", "title": "调度规则：如何使用 NodeAffinity", "body": [{"type": "button", "label": "什么是节点亲和性", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是节点亲和性（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是节点亲和性"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>节点规则:</strong> 节点必须匹配 `nodeAffinity` 中定义的规则，如节点上必须有标签（例如 `kubernetes.io/hostname`）。</div><div><strong>标签筛选:</strong>当操作符为存在、不存在时，不能填写筛选值。当为其他类型时，按定义过滤。</div></div>"}]}, {"type": "divider", "title": "节点标签 必须满足", "titlePosition": "left", "visibleOn": "${spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_node_affinity/ns/$metadata.namespace/name/$metadata.name", "quickSaveItemApi": "/k8s/$kind/group/$group/version/$version/update_node_affinity/ns/$metadata.namespace/name/$metadata.name", "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": [{"type": "form", "mode": "horizontal", "id": "affinity_add_form", "api": "/k8s/$kind/group/$group/version/$version/add_node_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-text", "name": "key", "label": "键", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择标签（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/labels/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "affinity_add_form", "args": {"value": {"key": "${event.data.item.key}", "values": "${event.data.item.value|asArray}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}, {"type": "select", "name": "operator", "label": "操作符", "required": true, "value": "In", "options": [{"label": "包含", "value": "In"}, {"label": "不包含", "value": "NotIn"}, {"label": "存在", "value": "Exists"}, {"label": "不存在", "value": "DoesNotExist"}]}, {"name": "values", "type": "input-array", "label": "值", "inline": true, "items": {"type": "input-text", "clearable": false}}]}]}}], "showIndex": true, "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_node_affinity/ns/$metadata.namespace/name/$metadata.name"}], "toggled": true}, {"name": "key", "label": "键", "width": "250px"}, {"name": "operator", "label": "操作符", "type": "mapping", "map": {"In": "<span class='label label-success'>包含</span>", "NotIn": "<span class='label label-warning'>不包含</span>", "Exists": "<span class='label label-info'>存在</span>", "DoesNotExist": "<span class='label label-info'>不存在</span>"}, "placeholder": "-", "width": "100px", "quickEdit": {"type": "select", "options": [{"label": "包含", "value": "In"}, {"label": "不包含", "value": "NotIn"}, {"label": "存在", "value": "Exists"}, {"label": "不存在", "value": "DoesNotExist"}], "saveImmediately": true}}, {"name": "values", "label": "值", "type": "each", "items": {"type": "tpl", "tpl": "${item}<br>"}, "placeholder": "-", "quickEdit": {"type": "input-array", "inline": true, "items": {"type": "input-text", "clearable": false}, "saveImmediately": true}}]}, {"type": "wrapper", "className": "h-10", "visibleOn": "${spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "divider", "title": "节点标签 优先满足", "titlePosition": "left", "visibleOn": "${spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"name": "spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution", "visibleOn": "${spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "type": "each", "items": {"type": "table", "source": "${item.preference.matchExpressions}", "columns": [{"name": "key", "label": "键", "width": "250px"}, {"name": "operator", "label": "操作符", "type": "mapping", "map": {"In": "<span class='label label-success'>包含</span>", "NotIn": "<span class='label label-warning'>不包含</span>", "Exists": "<span class='label label-info'>存在</span>"}, "placeholder": "-", "width": "50px"}, {"name": "values", "label": "值", "type": "each", "items": {"type": "tpl", "tpl": "${item}<br>"}, "placeholder": "-"}]}}]}, {"title": "Pod亲和性", "tab": [{"type": "panel", "title": "调度规则：如何使用 PodAffinity 和 TopologyKey", "body": [{"type": "button", "label": "什么是Pod亲和性", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是Pod亲和性（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是Pod亲和性"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>1、节点规则:</strong> 节点必须要有拓扑键同名的（如 topology.kubernetes.io/zone）标签。</div><div><strong>2、Pod标签:</strong> 节点上的 Pod 必须带有满足 labelSelector 中定义的标签（如 app=myapp）。</div><div>筛选出满足以上两条规则的节点，k8s将Pod调度到该节点上。</div></div>"}]}, {"type": "divider", "title": "Pod标签 必须满足", "titlePosition": "left", "visibleOn": "${spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_pod_affinity/ns/$metadata.namespace/name/$metadata.name", "showIndex": true, "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": {"type": "form", "id": "pod_affinity_form_add", "api": "post:/k8s/$kind/group/$group/version/$version/add_pod_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签", "type": "input-kv", "draggable": false}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "input-text", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择标签（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/labels/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "pod_affinity_form_add", "args": {"value": {"topologyKey": "${event.data.item.key}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}]}}}], "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_pod_affinity/ns/$metadata.namespace/name/$metadata.name"}, {"icon": "fa fa-edit text-primary", "type": "button", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "title": "修改", "body": {"type": "form", "api": "post:/k8s/$kind/group/$group/version/$version/update_pod_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签选择器", "type": "input-kv", "draggable": false, "required": true}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "static"}]}}}], "toggled": true}, {"type": "json", "label": "标签", "source": "${labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text"}]}, {"type": "wrapper", "className": "h-10", "visibleOn": "${spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "divider", "title": "Pod标签 优先满足", "titlePosition": "left", "visibleOn": "${spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "table", "visibleOn": "${spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "source": "${spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "columns": [{"type": "json", "label": "标签", "source": "${podAffinityTerm.labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text", "value": "${podAffinityTerm.topologyKey|raw}"}, {"name": "weight", "label": "权重", "type": "text", "value": "${weight}"}]}]}, {"title": "Pod反亲和性", "tab": [{"type": "panel", "title": "调度规则：如何使用 PodAntiAffinity 和 TopologyKey", "body": [{"type": "button", "label": "什么是Pod反亲和性", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是Pod反亲和性（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是Pod反亲和性"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>1、节点规则:</strong> 节点必须要有拓扑键同名的（如 topology.kubernetes.io/zone）标签。</div><div><strong>2、Pod标签:</strong> 节点上的 Pod 标签满足 labelSelector 中定义的标签（如 app=myapp），那么该节点排除。</div><div><strong></strong> 从带有拓扑键的主机列表中，但是排除运行某些pod的节点。例如，若一个节点已经有了某个特定标签的 Pod，新的 Pod 将避免调度到该节点。</div></div>"}]}, {"type": "divider", "title": "Pod标签 必须满足", "titlePosition": "left", "visibleOn": "${spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name", "showIndex": true, "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": {"type": "form", "id": "add_pod_affinity_form", "api": "post:/k8s/$kind/group/$group/version/$version/add_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签", "type": "input-kv", "draggable": false}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "input-text", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择标签（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/labels/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "add_pod_affinity_form", "args": {"value": {"topologyKey": "${event.data.item.key}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}]}}}], "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name"}, {"icon": "fa fa-edit text-primary", "type": "button", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "title": "修改", "body": {"type": "form", "id": "edit-pod-anti-affinity", "api": "post:/k8s/$kind/group/$group/version/$version/update_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签选择器", "type": "input-kv", "draggable": false, "required": true}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "static", "required": true}]}}}], "toggled": true}, {"type": "json", "label": "标签", "source": "${labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text"}]}, {"type": "wrapper", "className": "h-10", "visibleOn": "${spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "divider", "title": "Pod标签 优先满足", "titlePosition": "left", "visibleOn": "${spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "table", "visibleOn": "${spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "source": "${spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "columns": [{"type": "json", "label": "标签", "source": "${podAffinityTerm.labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text", "value": "${podAffinityTerm.topologyKey|raw}"}, {"name": "weight", "label": "权重", "type": "text", "value": "${weight}"}]}]}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "spec.template.spec.containers", "label": "容器", "type": "each", "items": {"type": "tpl", "tpl": "<div style='margin-bottom: 10px;'><strong class='text-green-500 font-black'>${name}</strong>: ${image|simpleImageName}</div>"}}, {"type": "progress", "mode": "line", "value": "<%= (data.status.numberReady / data.status.desiredNumberScheduled * 100).toFixed(2) %>", "striped": true, "label": "当前状态", "animate": true, "width": "150px"}, {"name": "spec.updateStrategy.type", "label": "策略", "type": "mapping", "map": {"RollingUpdate": "<span class='label label-primary'>滚动更新</span>", "OnDelete": "<span class='label label-warning'>删除时触发</span>"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}