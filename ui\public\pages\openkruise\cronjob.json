{"type": "page", "data": {"ns": "${ls:selectedNs||'default'}", "kind": "AdvancedCronJob", "version": "v1alpha1", "group": "apps.kruise.io"}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}, {"type": "button", "label": "属性文档", "level": "link", "icon": "fas fa-book-open text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 属性文档（ESC 关闭）", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/kind/$kind/group/$group/version/$version", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}}, {"type": "button", "label": "指南", "level": "link", "icon": "fas fa-lightbulb text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}"}}]}}, {"type": "button", "label": "智检", "level": "link", "icon": "fas fa-walking text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI 智能巡检 ${kind} （ESC 关闭）", "body": [{"type": "k8sGPT", "api": "/k8s/k8s_gpt/kind/${kind}/run"}]}}, {"label": "创建", "icon": "fas fa-dharmachakra text-primary", "type": "button", "level": "link", "actionType": "url", "blank": true, "url": "/#/apply/apply?kind=${kind}"}]}, {"type": "container", "className": "floating-toolbar-right", "body": [{"type": "wrapper", "body": [{"type": "form", "mode": "inline", "wrapWithPanel": false, "body": [{"label": "命名空间", "type": "select", "multiple": true, "maxTagCount": 1, "name": "ns", "id": "ns", "searchable": true, "checkAll": true, "source": "/k8s/ns/option_list", "value": "${ls:selectedNs||'default'}", "onEvent": {"change": {"actions": [{"actionType": "reload", "componentId": "detailCRUD", "data": {"ns": "${ns}"}}, {"actionType": "custom", "script": "localStorage.setItem('selectedNs', event.data.ns)"}]}}}, {"label": "集群", "type": "select", "multiple": false, "name": "cluster", "id": "cluster", "searchable": true, "source": "/params/cluster/option_list", "value": "${ls:cluster}", "onEvent": {"change": {"actions": [{"actionType": "custom", "script": "localStorage.setItem('cluster', event.data.value)"}, {"actionType": "custom", "script": "window.location.reload();"}]}}}]}]}]}, {"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload", "bulkActions"], "loadDataOnce": false, "syncLocation": false, "initFetch": true, "perPage": 10, "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/batch/remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}, {"label": "强制删除", "actionType": "ajax", "confirmText": "确定要批量强制删除?", "api": {"url": "/k8s/$kind/group/$group/version/$version/force_remove", "method": "post", "data": {"name_list": "${selectedItems | pick:metadata.name }", "ns_list": "${selectedItems | pick:metadata.namespace }"}}}], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "api": "post:/k8s/$kind/group/$group/version/$version/list/ns/${ns}", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/$kind/group/$group/version/$version/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "name": "${metadata.name}", "namespace": "${metadata.namespace}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group/${group}/version/${version}/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}]}], "actions": []}}, {"type": "dropdown-button", "level": "link", "buttons": [{"type": "button", "icon": "fas fa-calendar-alt text-primary", "label": "查看事件", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "查看事件   (ESC 关闭)", "body": [{"type": "crud", "id": "detailEvent", "name": "detailEvent", "headerToolbar": ["reload", {"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "loadDataOnce": true, "syncLocation": false, "perPage": 10, "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name/event", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "icon": "fas fa-brain text-primary", "label": "AI问诊", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI 查询", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/event", "params": {"note": "${note}", "source": "${source}", "reason": "${reason}", "reportingController": "${reportingController}", "type": "${type}", "regardingKind": "${kind}"}}]}, "visibleOn": "type === 'Warning'"}], "toggled": true}, {"name": "type", "label": "类型", "filterable": {"options": [{"label": "正常 ", "value": "Normal"}, {"label": "告警 ", "value": "Warning"}]}, "type": "mapping", "map": {"Normal": "<span class='label label-success'>正常</span>", "Warning": "<span class='label label-danger'>告警</span>"}}, {"name": "reason", "label": "原因", "type": "text"}, {"name": "field", "label": "关联字段", "type": "tpl", "tpl": "${regarding.fieldPath}"}, {"name": "source", "label": "事件来源", "type": "tpl", "tpl": "${reportingController} ${reportingInstance}"}, {"name": "note", "label": "说明", "type": "text", "searchable": true}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}}]}], "toggled": true}, {"name": "metadata.namespace", "label": "命名空间", "type": "text", "sortable": true}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px", "sortable": true, "searchable": {"type": "input-text", "name": "metadata.name", "clearable": true, "label": "名称", "placeholder": "输入名称"}}, {"name": "spec.schedule", "label": "调度计划", "type": "control", "body": [{"type": "tpl", "tpl": "${spec.schedule}"}, {"type": "button", "label": "查", "level": "link", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI释义: ${spec.schedule}", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/cron", "params": {"cron": "${spec.schedule}"}}]}}]}, {"name": "status.lastScheduleTime", "label": "最后调度", "type": "k8sAge"}, {"name": "status.active", "label": "运行中", "type": "tpl", "tpl": "${count(status.active)}"}, {"name": "spec.suspend", "label": "暂停", "type": "mapping", "map": {"false": "<span class='label label-success'>正常调度</span>", "true": "<span class='label label-danger'>暂停调度</span>"}}, {"name": "spec.concurrencyPolicy", "label": "并发策略", "type": "mapping", "map": {"Allow": "<span class='label label-success'>允许并发</span>", "Replace": "<span class='label label-success'>替换运行</span>", "Forbid": "<span class='label label-danger'>禁止并发</span>"}}, {"label": "保留策略", "type": "tpl", "tpl": "成功保留: ${spec.successfulJobsHistoryLimit || 0} <br/> 失败保留: ${spec.failedJobsHistoryLimit || 0}"}, {"name": "spec.template.broadcastJobTemplate.spec.template.spec.containers", "label": "容器", "type": "each", "items": {"type": "tpl", "tpl": "<div style='margin-bottom: 10px;'><strong class='text-green-500 font-black'>${name}</strong>: ${image|simpleImageName}</div>"}}, {"name": "metadata.labels", "label": "标签", "type": "tpl", "tpl": "${metadata.labels ? '<i class=\"fa fa-tags text-primary\"></i>' : '<i class=\"fa fa-tags text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 标签 (ESC 关闭)", "name": "dialog_labels", "size": "lg", "closeOnEsc": true, "closeOnOutside": true, "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_labels/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "labels", "draggable": false, "value": "${metadata.labels}"}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "metadata.annotations", "label": "注解", "type": "tpl", "tpl": "${metadata.annotations|filterAnnotations|showAnnotationIcon|isTrue:'<i class=\"fa fa-note-sticky text-primary\"></i>':'<i class=\"fa fa-note-sticky text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"title": "${metadata.name} 注解 (ESC 关闭)", "name": "dialog_annotations", "body": [{"type": "form", "mode": "horizontal", "labelWidth": 0, "api": "post:/k8s/$kind/group/$group/version/$version/update_annotations/ns/$metadata.namespace/name/$metadata.name", "initApi": "get:/k8s/$kind/group/$group/version/$version/annotations/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-kv", "name": "annotations", "draggable": false, "value": "${annotations}"}]}], "size": "lg", "closeOnEsc": true, "closeOnOutside": true}}]}}, "style": {"cursor": "pointer"}}, {"name": "spec.jobTemplate.spec.template.spec.tolerations", "label": "容忍度", "type": "tpl", "tpl": "${spec.jobTemplate.spec.template.spec.tolerations ? '<i class=\"fa-solid fa-exclamation-triangle text-primary\"></i>' : '<i class=\"fa-solid fa-exclamation-triangle text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${metadata.name} 容忍度 (ESC 关闭)", "body": [{"type": "panel", "title": "调度规则：如何使用 容忍度", "body": [{"type": "button", "label": "什么是容忍度", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是容忍度（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是容忍度"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>容忍度规则:</strong> Pod 必须容忍节点上的污点（Taints），才能调度到该节点。容忍度由 key、operator、value 和 effect 组成，其中 key 代表污点键，operator 决定匹配方式，effect 代表污点的影响类型。</div><div><strong>匹配规则:</strong><br>1. key 和 effect 必须与节点上的污点完全一致，否则容忍度无效。<br>2. operator 决定 value 是否必须匹配：当 operator 为 Equal 时，value 也必须与节点污点的 value 一致；当 operator 为 Exists 时，只要 key 存在即可，不关心 value。</div><div><strong>调度行为:</strong> 仅当 Pod 的容忍度与节点污点的 key 和 effect 匹配，并且 value 符合 operator 规则时，Pod 才能调度到该节点，否则调度将被阻止。</div></div>"}]}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_tolerations/ns/$metadata.namespace/name/$metadata.name", "quickSaveItemApi": "/k8s/$kind/group/$group/version/$version/update_tolerations/ns/$metadata.namespace/name/$metadata.name", "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": [{"type": "form", "mode": "horizontal", "id": "toleration_add_form", "api": "/k8s/$kind/group/$group/version/$version/add_tolerations/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-text", "name": "key", "label": "键", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "污点速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择污点（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/taints/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "toleration_add_form", "args": {"value": {"key": "${event.data.item.key}", "value": "${event.data.item.value}", "effect": "${event.data.item.effect}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "effect", "label": "效果", "searchable": {"type": "select", "options": [{"label": "禁止调度", "value": "NoSchedule"}, {"label": "优先不调度", "value": "PreferNoSchedule"}, {"label": "不可执行", "value": "NoExecute"}], "name": "effect", "clearable": true, "placeholder": "输选择效果"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}, {"type": "select", "name": "operator", "label": "操作符", "required": true, "value": "Equal", "options": [{"label": "等于", "value": "Equal"}, {"label": "存在", "value": "Exists"}]}, {"name": "value", "type": "input-text", "label": "值", "visibleOn": "${operator === 'Equal'}"}, {"name": "effect", "label": "效果", "required": true, "value": "NoSchedule", "type": "select", "options": [{"label": "禁止调度", "value": "NoSchedule"}, {"label": "优先不调度", "value": "PreferNoSchedule"}, {"label": "不可执行", "value": "NoExecute"}]}]}]}}], "showIndex": true, "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_tolerations/ns/$metadata.namespace/name/$metadata.name"}], "toggled": true}, {"name": "key", "label": "键"}, {"name": "operator", "label": "操作符", "placeholder": "-", "type": "mapping", "map": {"Equal": "<span class='label label-success'>等于</span>", "Exists": "<span class='label label-success'>存在</span>"}, "quickEdit": {"type": "select", "options": [{"label": "等于", "value": "Equal"}, {"label": "存在", "value": "Exists"}], "saveImmediately": true}}, {"name": "value", "label": "值", "placeholder": "-", "quickEdit": {"type": "input-text", "saveImmediately": true}}, {"name": "effect", "label": "效果", "type": "mapping", "map": {"NoSchedule": "<span class='label label-warning'>禁止调度</span>", "PreferNoSchedule": "<span class='label label-danger'>优先不调度</span>", "NoExecute": "<span class='label label-danger'>不可执行</span>"}}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "spec.jobTemplate.spec.template.spec.affinity", "label": "亲和性", "type": "tpl", "tpl": "${spec.jobTemplate.spec.template.spec.affinity ? '<i class=\"fa-solid fa-sitemap text-primary\"></i>' : '<i class=\"fa-solid fa-sitemap text-secondary\"></i>'}", "onEvent": {"click": {"actions": [{"actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${metadata.name} 亲和性 (ESC 关闭)", "body": [{"type": "tabs", "swipeable": true, "tabs": [{"title": "节点亲和性", "tab": [{"type": "panel", "title": "调度规则：如何使用 NodeAffinity", "body": [{"type": "button", "label": "什么是节点亲和性", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是节点亲和性（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是节点亲和性"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>节点规则:</strong> 节点必须匹配 `nodeAffinity` 中定义的规则，如节点上必须有标签（例如 `kubernetes.io/hostname`）。</div><div><strong>标签筛选:</strong>当操作符为存在、不存在时，不能填写筛选值。当为其他类型时，按定义过滤。</div></div>"}]}, {"type": "divider", "title": "节点标签 必须满足", "titlePosition": "left", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_node_affinity/ns/$metadata.namespace/name/$metadata.name", "quickSaveItemApi": "/k8s/$kind/group/$group/version/$version/update_node_affinity/ns/$metadata.namespace/name/$metadata.name", "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": [{"type": "form", "mode": "horizontal", "id": "affinity_add_form", "api": "/k8s/$kind/group/$group/version/$version/add_node_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "input-text", "name": "key", "label": "键", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择标签（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/labels/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "affinity_add_form", "args": {"value": {"key": "${event.data.item.key}", "values": "${event.data.item.value|asArray}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}, {"type": "select", "name": "operator", "label": "操作符", "required": true, "value": "In", "options": [{"label": "包含", "value": "In"}, {"label": "不包含", "value": "NotIn"}, {"label": "存在", "value": "Exists"}, {"label": "不存在", "value": "DoesNotExist"}]}, {"name": "values", "type": "input-array", "label": "值", "inline": true, "items": {"type": "input-text", "clearable": false}}]}]}}], "showIndex": true, "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_node_affinity/ns/$metadata.namespace/name/$metadata.name"}], "toggled": true}, {"name": "key", "label": "键", "width": "250px"}, {"name": "operator", "label": "操作符", "type": "mapping", "map": {"In": "<span class='label label-success'>包含</span>", "NotIn": "<span class='label label-warning'>不包含</span>", "Exists": "<span class='label label-info'>存在</span>", "DoesNotExist": "<span class='label label-info'>不存在</span>"}, "placeholder": "-", "width": "100px", "quickEdit": {"type": "select", "options": [{"label": "包含", "value": "In"}, {"label": "不包含", "value": "NotIn"}, {"label": "存在", "value": "Exists"}, {"label": "不存在", "value": "DoesNotExist"}], "saveImmediately": true}}, {"name": "values", "label": "值", "type": "each", "items": {"type": "tpl", "tpl": "${item}<br>"}, "placeholder": "-", "quickEdit": {"type": "input-array", "inline": true, "items": {"type": "input-text", "clearable": false}, "saveImmediately": true}}]}, {"type": "wrapper", "className": "h-10", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "divider", "title": "节点标签 优先满足", "titlePosition": "left", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"name": "spec.jobTemplate.spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.nodeAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "type": "each", "items": {"type": "table", "source": "${item.preference.matchExpressions}", "columns": [{"name": "key", "label": "键", "width": "250px"}, {"name": "operator", "label": "操作符", "type": "mapping", "map": {"In": "<span class='label label-success'>包含</span>", "NotIn": "<span class='label label-warning'>不包含</span>", "Exists": "<span class='label label-info'>存在</span>"}, "placeholder": "-", "width": "50px"}, {"name": "values", "label": "值", "type": "each", "items": {"type": "tpl", "tpl": "${item}<br>"}, "placeholder": "-"}]}}]}, {"title": "Pod亲和性", "tab": [{"type": "panel", "title": "调度规则：如何使用 PodAffinity 和 TopologyKey", "body": [{"type": "button", "label": "什么是Pod亲和性", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是Pod亲和性（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是Pod亲和性"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>1、节点规则:</strong> 节点必须要有拓扑键同名的（如 topology.kubernetes.io/zone）标签。</div><div><strong>2、Pod标签:</strong> 节点上的 Pod 必须带有满足 labelSelector 中定义的标签（如 app=myapp）。</div><div>筛选出满足以上两条规则的节点，k8s将Pod调度到该节点上。</div></div>"}]}, {"type": "divider", "title": "Pod标签 必须满足", "titlePosition": "left", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_pod_affinity/ns/$metadata.namespace/name/$metadata.name", "showIndex": true, "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": {"type": "form", "id": "pod_affinity_form_add", "api": "post:/k8s/$kind/group/$group/version/$version/add_pod_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签", "type": "input-kv", "draggable": false}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "input-text", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择标签（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/labels/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "pod_affinity_form_add", "args": {"value": {"topologyKey": "${event.data.item.key}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}]}}}], "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_pod_affinity/ns/$metadata.namespace/name/$metadata.name"}, {"icon": "fa fa-edit text-primary", "type": "button", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "title": "修改", "body": {"type": "form", "api": "post:/k8s/$kind/group/$group/version/$version/update_pod_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签选择器", "type": "input-kv", "draggable": false, "required": true}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "static"}]}}}], "toggled": true}, {"type": "json", "label": "标签", "source": "${labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text"}]}, {"type": "wrapper", "className": "h-10", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "divider", "title": "Pod标签 优先满足", "titlePosition": "left", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "table", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "source": "${spec.jobTemplate.spec.template.spec.affinity.podAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "columns": [{"type": "json", "label": "标签", "source": "${podAffinityTerm.labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text", "value": "${podAffinityTerm.topologyKey|raw}"}, {"name": "weight", "label": "权重", "type": "text", "value": "${weight}"}]}]}, {"title": "Pod反亲和性", "tab": [{"type": "panel", "title": "调度规则：如何使用 PodAntiAffinity 和 TopologyKey", "body": [{"type": "button", "label": "什么是Pod反亲和性", "level": "link", "icon": "fas fa-question-circle text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "什么是Pod反亲和性（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_question", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "question": "什么是Pod反亲和性"}}]}}, {"type": "tpl", "tpl": "<div><div><strong>1、节点规则:</strong> 节点必须要有拓扑键同名的（如 topology.kubernetes.io/zone）标签。</div><div><strong>2、Pod标签:</strong> 节点上的 Pod 标签满足 labelSelector 中定义的标签（如 app=myapp），那么该节点排除。</div><div><strong></strong> 从带有拓扑键的主机列表中，但是排除运行某些pod的节点。例如，若一个节点已经有了某个特定标签的 Pod，新的 Pod 将避免调度到该节点。</div></div>"}]}, {"type": "divider", "title": "Pod标签 必须满足", "titlePosition": "left", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "crud", "api": "/k8s/$kind/group/$group/version/$version/list_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name", "showIndex": true, "headerToolbar": [{"type": "button", "label": "新增规则", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "新增", "body": {"type": "form", "id": "add_pod_affinity_form", "api": "post:/k8s/$kind/group/$group/version/$version/add_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签", "type": "input-kv", "draggable": false}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "input-text", "required": true, "addOn": {"type": "button", "icon": "fa fa-filter", "label": "速选", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "从已有节点上选择标签（ESC 关闭）", "body": [{"type": "crud", "api": "get:/k8s/node/labels/list", "onEvent": {"rowClick": {"actions": [{"actionType": "setValue", "componentId": "add_pod_affinity_form", "args": {"value": {"topologyKey": "${event.data.item.key}"}}}, {"actionType": "closeDialog"}]}}, "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false, "defaultCollapsed": false}, "selectable": true, "primaryField": "key", "loadDataOnce": true, "initFetch": true, "perPage": 5, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "columns": [{"name": "key", "label": "键", "searchable": {"type": "input-text", "name": "key", "clearable": true, "label": "标签键", "placeholder": "输入键名称"}, "width": "100px"}, {"name": "value", "label": "值", "searchable": {"type": "input-text", "name": "value", "clearable": true, "label": "标签值", "placeholder": "输入值名称"}, "width": "100px"}, {"name": "names", "label": "节点名称", "type": "tpl", "tpl": "${names|join:','},", "width": "200px", "searchable": true}, {"name": "ips", "label": "节点IP", "type": "tpl", "tpl": "${ips|join:','},", "width": "200px", "searchable": true}]}]}}}]}}}], "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-trash text-primary", "actionType": "ajax", "tooltip": "删除", "api": "/k8s/$kind/group/$group/version/$version/delete_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name"}, {"icon": "fa fa-edit text-primary", "type": "button", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "title": "修改", "body": {"type": "form", "id": "edit-pod-anti-affinity", "api": "post:/k8s/$kind/group/$group/version/$version/update_pod_anti_affinity/ns/$metadata.namespace/name/$metadata.name", "body": [{"name": "labelSelector.matchLabels", "label": "Pod标签选择器", "type": "input-kv", "draggable": false, "required": true}, {"name": "<PERSON><PERSON><PERSON>", "label": "节点拓扑键", "type": "static", "required": true}]}}}], "toggled": true}, {"type": "json", "label": "标签", "source": "${labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text"}]}, {"type": "wrapper", "className": "h-10", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "divider", "title": "Pod标签 优先满足", "titlePosition": "left", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}"}, {"type": "table", "visibleOn": "${spec.jobTemplate.spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "source": "${spec.jobTemplate.spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution}", "columns": [{"type": "json", "label": "标签", "source": "${podAffinityTerm.labelSelector.matchLabels}", "levelExpand": 1}, {"name": "<PERSON><PERSON><PERSON>", "label": "拓扑键", "type": "text", "value": "${podAffinityTerm.topologyKey|raw}"}, {"name": "weight", "label": "权重", "type": "text", "value": "${weight}"}]}]}]}]}}]}}, "style": {"cursor": "pointer"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}