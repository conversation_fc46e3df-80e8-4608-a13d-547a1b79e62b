.container {
  width: 100vw;
  height: 100vh;
  .navbar {
    min-width: 100vw;
    height: 64px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
  }
  .logo {
    padding: 10px 15px;
    cursor: pointer;
    :local {
      h1 {
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        color: #007bff;
        font-size: 24px;
        text-indent: 5px;
      }
      img {
        height: 36px;
      }
    }
  }
 
  .collapse {
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 2px;
    bottom: 12px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    position: absolute;
    right: 12px;
    width: 24px;
  }
  .content {
    padding: 16px;
    height: calc(100vh - 60px);
    background: #f0f2f5;
    box-sizing: border-box;
  }
  :global {
    .ant-layout-sider {
      background: #fff;
    }
    .ant-layout-sider-trigger {
      background: #fff;
      border-top: 1px solid #f0f0f0;
      text-align: right;
      padding-right: 12px;
    }
  }
}