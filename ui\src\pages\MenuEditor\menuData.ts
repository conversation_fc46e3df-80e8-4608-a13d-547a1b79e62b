import {MenuItem} from '@/types/menu';

export const initialMenu: MenuItem[] = [
    {
        key: 'cluster_user',
        title: '多集群',
        icon: 'fa-solid fa-server',
        eventType: 'custom',
        customEvent: '() => loadJsonPage("/user/cluster/cluster_user")',
        order: 1,
    },
    {
        key: 'cluster_summary',
        title: '集群总览',
        icon: 'fa-solid fa-chart-pie',
        eventType: 'custom',
        customEvent: '() => loadJsonPage("/cluster/summary_view")',
        order: 2,
    },
    {
        key: 'cluster_ns',
        title: '命名空间',
        icon: 'fa-solid fa-border-style',
        eventType: 'custom',
        customEvent: '() => loadJsonPage("/cluster/ns")',
        order: 3,
    },
    {
        key: 'cluster_node',
        title: '节点',
        icon: 'fa-solid fa-computer',
        eventType: 'custom',
        customEvent: '() => loadJsonPage("/cluster/node")',
        order: 4,
    },
    {
        key: 'event',
        title: '事件',
        icon: 'fa-solid fa-bell',
        eventType: 'custom',
        customEvent: '() => loadJsonPage("/ns/event")',
        order: 5,
    },
    {
        key: 'workload',
        title: '工作负载',
        icon: 'fa-solid fa-cube',
        order: 6,
        children: [
            {
                key: 'deploy',
                title: '部署',
                icon: 'fa-solid fa-layer-group',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/deploy")',
                order: 1,
            },
            {
                key: 'statefulset',
                title: '有状态集',
                icon: 'fa-solid fa-database',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/statefulset")',
                order: 2,
            },
            {
                key: 'daemonset',
                title: '守护进程集',
                icon: 'fa-solid fa-shield-halved',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/daemonset")',
                order: 3,
            },
            {
                key: 'job',
                title: '任务',
                icon: 'fa-solid fa-list-check',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/job")',
                order: 4,
            },
            {
                key: 'cronjob',
                title: '定时任务',
                icon: 'fa-solid fa-clock',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/cronjob")',
                order: 5,
            },
            {
                key: 'pod',
                title: '容器组',
                icon: 'fa-solid fa-cubes',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/pod")',
                order: 6,
            },
            {
                key: 'replicaset',
                title: '副本集',
                icon: 'fa-solid fa-clone',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/replicaset")',
                order: 7,
            },
        ],
    },
    {
        key: 'crd',
        title: 'CRD',
        icon: 'fa-solid fa-file-code',
        order: 7,
        children: [
            {
                key: 'custom_resource',
                title: '自定义资源',
                icon: 'fa-solid fa-gears',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/crd/crd")',
                order: 1,
            },
        ],
    },
    {
        key: 'OpenKruise-workload',
        title: 'OpenKruise',
        icon: 'fa-solid fa-cube',
        order: 8,
        show: 'isOpenKruiseSupported()==true',
        children: [
            {
                key: 'advanced-cloneset',
                title: '克隆集',
                icon: 'fa-solid fa-clone',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/cloneset")',
                order: 1,
            },
            {
                key: 'advanced-statefulset',
                title: '高级有状态集',
                icon: 'fa-solid fa-layer-group',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/statefulset")',
                order: 2,
            },
            {
                key: 'advanced-daemonSet',
                title: '高级守护进程集',
                icon: 'fa-solid fa-shield-halved',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/daemonset")',
                order: 3,
            },
            {
                key: 'advanced-cronJob',
                title: '高级定时任务',
                icon: 'fa-solid fa-clock',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/cronjob")',
                order: 4,
            },
            {
                key: 'broadcast-job',
                title: '广播作业任务',
                icon: 'fa-solid fa-broadcast-tower',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/broadcastjob")',
                order: 5,
            },
            {
                key: 'sidecarset',
                title: '边车集',
                icon: 'fa-solid fa-car-side',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/sidecarset")',
                order: 6,
            },
            {
                key: 'workload-spread',
                title: '工作负载分布',
                icon: 'fa-solid fa-arrows-split-up-and-left',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/workloadspread")',
                order: 7,
            },
            {
                key: 'united-deployment',
                title: '联合部署',
                icon: 'fa-solid fa-object-group',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/uniteddeployment")',
                order: 8,
            },
            {
                key: 'container_recreate_request',
                title: '容器重建请求',
                icon: 'fa-solid fa-recycle',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/container_recreate_request")',
                order: 9,
            },
            {
                key: 'imagepulljob',
                title: '镜像拉取作业',
                icon: 'fa-solid fa-cloud-arrow-down',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/imagepulljob")',
                order: 10,
            },
            {
                key: 'persistentpodstate',
                title: '持久化状态',
                icon: 'fa-solid fa-database',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/persistentpodstate")',
                order: 11,
            },
            {
                key: 'podprobemarker',
                title: 'Pod探测标记',
                icon: 'fa-solid fa-magnifying-glass',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/podprobemarker")',
                order: 12,
            },
            {
                key: 'PodUnavailableBudget',
                title: 'Pod不可用预算',
                icon: 'fa-solid fa-circle-exclamation',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/PodUnavailableBudget")',
                order: 13,
            },
            {
                key: 'ResourceDistribution',
                title: '资源分发',
                icon: 'fa-solid fa-share-nodes',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/openkruise/ResourceDistribution")',
                order: 14,
            },
        ],
    },
    {
        key: 'istio',
        title: 'Istio',
        icon: 'fa-solid fa-cube',
        order: 9,
        show: 'isIstioSupported()==true',
        children: [
            {
                key: 'isito-VirtualService',
                title: '虚拟服务',
                icon: 'fa-solid fa-route',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/VirtualService")',
                order: 1,
            },
            {
                key: 'istio-DestinationRule',
                title: '目标规则',
                icon: 'fa-solid fa-location-dot',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/DestinationRule")',
                order: 2,
            },
            {
                key: 'istio-EnvoyFilter',
                title: 'Envoy过滤器',
                icon: 'fa-solid fa-filter',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/EnvoyFilter")',
                order: 3,
            },
            {
                key: 'istio-Gateway',
                title: '网关',
                icon: 'fa-solid fa-network-wired',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/Gateway")',
                order: 4,
            },
            {
                key: 'istio-PeerAuthentication',
                title: '对等认证',
                icon: 'fa-solid fa-user-shield',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/PeerAuthentication")',
                order: 5,
            },
            {
                key: 'istio-ProxyConfig',
                title: '代理配置',
                icon: 'fa-solid fa-gears',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/ProxyConfig")',
                order: 6,
            },
            {
                key: 'istio-RequestAuthentication',
                title: '请求认证',
                icon: 'fa-solid fa-key',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/RequestAuthentication")',
                order: 7,
            },
            {
                key: 'istio-ServiceEntry',
                title: '服务入口',
                icon: 'fa-solid fa-door-open',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/ServiceEntry")',
                order: 8,
            },
            {
                key: 'istio-Sidecar',
                title: '边车',
                icon: 'fa-solid fa-car-side',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/Sidecar")',
                order: 9,
            },
            {
                key: 'istio-Telemetry',
                title: '遥测',
                icon: 'fa-solid fa-chart-line',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/Telemetry")',
                order: 10,
            },
            {
                key: 'istio-AuthorizationPolicy',
                title: '授权策略',
                icon: 'fa-solid fa-user-lock',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/AuthorizationPolicy")',
                order: 11,
            },
            {
                key: 'istio-WasmPlugin',
                title: 'Wasm插件',
                icon: 'fa-solid fa-puzzle-piece',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/WasmPlugin")',
                order: 12,
            },
            {
                key: 'istio-WorkloadEntry',
                title: '工作负载条目',
                icon: 'fa-solid fa-server',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/WorkloadEntry")',
                order: 13,
            },
            {
                key: 'istio-WorkloadGroup',
                title: '工作负载组',
                icon: 'fa-solid fa-people-group',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/istio/WorkloadGroup")',
                order: 14,
            },
        ],
    },
    {
        key: 'Helm',
        title: 'Helm应用',
        icon: 'fab fa-app-store',
        order: 10,
        children: [
            {
                key: 'helm_repo',
                title: '仓库',
                icon: 'fas fa-database',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/helm/repo")',
                order: 1,
                show: 'isPlatformAdmin()==true',
            },
            {
                key: 'helm_chart',
                title: '应用包',
                icon: 'fa-solid fa-cubes',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/helm/chart")',
                order: 2,
            },
            {
                key: 'helm_release',
                title: '应用实例',
                icon: 'fas fa-layer-group',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/helm/release")',
                order: 3,
            },
        ],
    },
    {
        key: 'config',
        title: '配置',
        icon: 'fa-solid fa-sliders-h',
        order: 11,
        children: [
            {
                key: 'configmap',
                title: '配置映射',
                icon: 'fa-solid fa-map',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/configmap")',
                order: 1,
            },
            {
                key: 'secret',
                title: '密钥',
                icon: 'fa-solid fa-key',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/secret")',
                order: 2,
            },
            {
                key: 'validation_webhook',
                title: '验证钩子',
                icon: 'fa-solid fa-check',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/validation_webhook")',
                order: 3,
            },
            {
                key: 'mutating_webhook',
                title: '变更钩子',
                icon: 'fa-solid fa-exchange',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/mutating_webhook")',
                order: 4,
            },
        ],
    },
    {
        key: 'network',
        title: '网络',
        icon: 'fa-solid fa-network-wired',
        order: 12,
        children: [
            {
                key: 'svc',
                title: 'SVC服务',
                icon: 'fa-solid fa-project-diagram',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/svc")',
                order: 1,
            },
            {
                key: 'ingress',
                title: 'Ingress入口',
                icon: 'fa-solid fa-wifi',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/ing")',
                order: 2,
            },
            {
                key: 'ingress_class',
                title: 'Ingress入口类',
                icon: 'fa-solid fa-sitemap',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/ingress_class")',
                order: 3,
            },
        ],
    },
    {
        key: 'GatewayAPI',
        title: '网关API',
        icon: 'fa-solid fa-door-closed',
        order: 13,
        show: 'isGatewayAPISupported()==true',
        children: [
            {
                key: 'gatewayapi_gateway_class',
                title: '网关类',
                icon: 'fa-solid fa-door-open',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/gateway_class")',
                order: 1,
            },
            {
                key: 'gatewayapi_gateway',
                title: '网关',
                icon: 'fa-solid fa-archway',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/gateway")',
                order: 2,
            },
            {
                key: 'gatewayapi_http_route',
                title: 'HTTP路由',
                icon: 'fa-solid fa-route',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/http_route")',
                order: 3,
            },
            {
                key: 'gatewayapi_grpc_route',
                title: 'GRPC路由',
                icon: 'fa-solid fa-code-branch',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/grpc_route")',
                order: 4,
            },
            {
                key: 'gatewayapi_tcp_route',
                title: 'TCP路由',
                icon: 'fa-solid fa-plug',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/tcp_route")',
                order: 5,
            },
            {
                key: 'gatewayapi_udp_route',
                title: 'UDP路由',
                icon: 'fa-solid fa-broadcast-tower',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/udp_route")',
                order: 6,
            },
            {
                key: 'gatewayapi_tls_route',
                title: 'TLS路由',
                icon: 'fa-solid fa-shield-alt',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/gatewayapi/tls_route")',
                order: 7,
            },
        ],
    },
    {
        key: 'storage',
        title: '存储',
        icon: 'fa-solid fa-memory',
        order: 14,
        children: [
            {
                key: 'pvc',
                title: '持久卷声明',
                icon: 'fa-solid fa-folder',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/pvc")',
                order: 1,
            },
            {
                key: 'pv',
                title: '持久卷',
                icon: 'fa-solid fa-hdd',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/pv")',
                order: 2,
            },
            {
                key: 'storage_class',
                title: '存储类',
                icon: 'fa-solid fa-coins',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/storage_class")',
                order: 3,
            },
        ],
    },
    {
        key: 'access_control',
        title: '访问控制',
        icon: 'fa-solid fa-lock',
        order: 15,
        children: [
            {
                key: 'service_account',
                title: '服务账户',
                icon: 'fa-solid fa-user-shield',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/service_account")',
                order: 1,
            },
            {
                key: 'role',
                title: '角色',
                icon: 'fa-solid fa-user-tag',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/role")',
                order: 2,
            },
            {
                key: 'role_binding',
                title: '角色绑定',
                icon: 'fa-solid fa-link',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/role_binding")',
                order: 3,
            },
            {
                key: 'cluster_role',
                title: '集群角色',
                icon: 'fa-solid fa-users',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/cluster_role")',
                order: 4,
            },
            {
                key: 'cluster_role_binding',
                title: '集群角色绑定',
                icon: 'fa-solid fa-user-lock',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/cluster_role_binding")',
                order: 5,
            },
        ],
    },
    {
        key: 'cluster_config',
        title: '集群配置',
        icon: 'fa-solid fa-cog',
        order: 16,
        children: [
            {
                key: 'endpoint',
                title: '端点',
                icon: 'fa-solid fa-ethernet',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/endpoint")',
                order: 1,
            },
            {
                key: 'endpointslice',
                title: '端点切片',
                icon: 'fa-solid fa-newspaper',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/endpointslice")',
                order: 2,
            },
            {
                key: 'hpa',
                title: '水平自动扩缩',
                icon: 'fa-solid fa-arrows-left-right',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/hpa")',
                order: 3,
            },
            {
                key: 'network_policy',
                title: '网络策略',
                icon: 'fa-solid fa-project-diagram',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/network_policy")',
                order: 4,
            },
            {
                key: 'resource_quota',
                title: '资源配额',
                icon: 'fa-solid fa-chart-pie',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/resource_quota")',
                order: 5,
            },
            {
                key: 'limit_range',
                title: '限制范围',
                icon: 'fa-solid fa-compress',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/limit_range")',
                order: 6,
            },
            {
                key: 'pdb',
                title: 'Pod中断配置',
                icon: 'fa-solid fa-receipt',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/pdb")',
                order: 7,
            },
            {
                key: 'lease',
                title: '租约',
                icon: 'fa-solid fa-file-contract',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/ns/lease")',
                order: 8,
            },
            {
                key: 'priority_class',
                title: '优先级类',
                icon: 'fa-solid fa-sort',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/priority_class")',
                order: 9,
            },
            {
                key: 'runtime_class',
                title: '运行时类',
                icon: 'fa-solid fa-play',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/runtime_class")',
                order: 10,
            },
            {
                key: 'csi_node',
                title: 'CSI节点',
                icon: 'fa-solid fa-server',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/csi_node")',
                order: 11,
            },
            {
                key: 'api_service',
                title: 'API 服务',
                icon: 'fa-solid fa-code',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/api_service")',
                order: 12,
            },
            {
                key: 'flow_schema',
                title: '流量规则',
                icon: 'fa-solid fa-random',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/flow_schema")',
                order: 13,
            },
            {
                key: 'priority_level_config',
                title: '优先级配置',
                icon: 'fa-solid fa-sliders',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/priority_level_config")',
                order: 14,
            },
            {
                key: 'component_status',
                title: '组件状态',
                icon: 'fa-solid fa-info-circle',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/cluster/component_status")',
                order: 15,
            },
        ],
    },

    {
        key: 'platform_settings',
        title: '平台设置',
        icon: 'fa-solid fa-wrench',
        order: 17,
        show: 'isPlatformAdmin()==true',
        children: [
            {
                key: 'cluster_all',
                title: '多集群管理',
                icon: 'fa-solid fa-server',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/cluster/cluster_all")',
                order: 1,
            },
            {
                key: 'system_config',
                title: '参数设置',
                icon: 'fa-solid fa-sliders',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/config/config")',
                order: 2,
            },
            {
                key: 'inspection_settings',
                title: '集群巡检设置',
                icon: 'fa-solid fa-stethoscope',
                order: 3,
                children: [
                    {
                        key: 'inspection_summary',
                        title: '巡检汇总',
                        icon: 'fa-solid fa-clipboard-list',
                        eventType: 'custom',
                        customEvent: '() => loadJsonPage("/admin/inspection/summary")',
                        order: 1,
                    },
                    {
                        key: 'inspection_schedule',
                        title: '巡检计划',
                        icon: 'fa-solid fa-stethoscope',
                        eventType: 'custom',
                        customEvent: '() => loadJsonPage("/admin/inspection/schedule")',
                        order: 2,
                    },
                    {
                        key: 'inspection_record',
                        title: '巡检记录',
                        icon: 'fa-solid fa-clipboard-list',
                        eventType: 'custom',
                        customEvent: '() => loadJsonPage("/admin/inspection/record")',
                        order: 3,
                    },
                    {
                        key: 'script_management',
                        title: '巡检规则',
                        icon: 'fa-solid fa-clipboard-check',
                        eventType: 'custom',
                        customEvent: '() => loadJsonPage("/admin/inspection/script")',
                        order: 4,
                    },
                    {
                        key: 'webhook_management',
                        title: 'webhook管理',
                        icon: 'fa-solid fa-bell-concierge',
                        eventType: 'custom',
                        customEvent: '() => loadJsonPage("/admin/inspection/webhook")',
                        order: 5,
                    },
                ],
            },
            {
                key: 'ai_model_config',
                title: 'AI模型配置',
                icon: 'fa-solid fa-sliders',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/config/ai_model_config")',
                order: 4,
            },
            {
                key: 'user_management',
                title: '用户管理',
                icon: 'fa-solid fa-user-gear',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/user/user")',
                order: 5,
            },
            {
                key: 'user_group_management',
                title: '用户组管理',
                icon: 'fa-solid fa-users-gear',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/user/user_group")',
                order: 6,
            },
            {
                key: 'mcp_management',
                title: 'MCP管理',
                icon: 'fa-solid fa-server',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/mcp/mcp")',
                order: 7,
            },
            {
                key: 'mcp_tool_log',
                title: 'MCP执行记录',
                icon: 'fa-solid fa-history',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/mcp/mcp_log")',
                order: 8,
            },
            {
                key: 'condition_reverse',
                title: '指标显示翻转',
                icon: 'fa-solid fa-arrows-rotate',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/config/condition")',
                order: 9,
            },
            {
                key: 'sso_config',
                title: '单点登录',
                icon: 'fa-solid fa-right-to-bracket',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/config/sso_config")',
                order: 10,
            },
            {
                key: 'ldap_config',
                title: 'LDAP登录',
                icon: 'fa-solid fa-right-to-bracket',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/admin/config/ldap_config")',
                order: 11,
            }

        ],
    },
    {
        key: 'user_profile',
        title: '个人中心',
        icon: 'fa-solid fa-user',
        order: 18,
        children: [
            {
                key: 'user_profile_login_settings',
                title: '登录设置',
                icon: 'fa-solid fa-key',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/user/profile/login_settings")',
                order: 1,
            },
            {
                key: 'user_profile_clusters',
                title: '我的集群',
                icon: 'fa-solid fa-server',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/user/profile/my_clusters")',
                order: 2,
            },
            {
                key: 'user_profile_api_keys',
                title: 'API密钥',
                icon: 'fa-solid fa-key',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/user/profile/api_keys")',
                order: 3,
            },
            {
                key: 'user_profile_mcp_keys',
                title: '开放MCP',
                icon: 'fa-solid fa-share-nodes',
                eventType: 'custom',
                customEvent: '() => loadJsonPage("/user/profile/mcp_keys")',
                order: 4,
            },
        ],
    },
    {
        key: 'open_api',
        title: '开放API',
        icon: 'fa-solid fa-code-branch',
        eventType: 'custom',
        customEvent: '() => open("/swagger/index.html")',
        order: 19,
    },
    {
        key: 'about',
        title: '关于',
        icon: 'fa-solid fa-circle-info',
        eventType: 'custom',
        customEvent: '() => loadJsonPage("/about/about")',
        order: 20,
    },
];