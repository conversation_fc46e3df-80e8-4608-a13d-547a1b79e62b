// 所有Font Awesome实心图标选项
const iconOptions = [
  { value: 'fa-0' },
        { value: 'fa-1' },
        { value: 'fa-2' },
        { value: 'fa-3' },
        { value: 'fa-4' },
        { value: 'fa-5' },
        { value: 'fa-6' },
        { value: 'fa-7' },
        { value: 'fa-8' },
        { value: 'fa-9' },
        { value: 'fa-a' },
        { value: 'fa-address-book' },
        { value: 'fa-address-card' },
        { value: 'fa-align-center' },
        { value: 'fa-align-justify' },
        { value: 'fa-align-left' },
        { value: 'fa-align-right' },
        { value: 'fa-anchor-lock' },
        { value: 'fa-anchor' },
        { value: 'fa-angle-down' },
        { value: 'fa-angle-left' },
        { value: 'fa-angle-right' },
        { value: 'fa-angle-up' },
        { value: 'fa-angles-down' },
        { value: 'fa-angles-left' },
        { value: 'fa-angles-right' },
        { value: 'fa-angles-up' },
        { value: 'fa-ankh' },
        { value: 'fa-apple-whole' },
        { value: 'fa-archway' },
        { value: 'fa-arrow-down' },
        { value: 'fa-arrow-left' },
        { value: 'fa-arrow-pointer' },
        { value: 'fa-arrow-right' },
        { value: 'fa-arrow-turn-up' },
        { value: 'fa-arrow-up-1-9' },
        { value: 'fa-arrow-up-9-1' },
        { value: 'fa-arrow-up-a-z' },
        { value: 'fa-arrow-up-long' },
        { value: 'fa-arrow-up-z-a' },
        { value: 'fa-arrow-up' },
        { value: 'fa-arrows-rotate' },
        { value: 'fa-arrows-spin' },
        { value: 'fa-arrows-to-dot' },
        { value: 'fa-arrows-to-eye' },
        { value: 'fa-asterisk' },
        { value: 'fa-at' },
        { value: 'fa-atom' },
        { value: 'fa-austral-sign' },
        { value: 'fa-award' },
        { value: 'fa-b' },
        { value: 'fa-baby-carriage' },
        { value: 'fa-baby' },
        { value: 'fa-backward-fast' },
        { value: 'fa-backward-step' },
        { value: 'fa-backward' },
        { value: 'fa-bacon' },
        { value: 'fa-bacteria' },
        { value: 'fa-bacterium' },
        { value: 'fa-bag-shopping' },
        { value: 'fa-bahai' },
        { value: 'fa-baht-sign' },
        { value: 'fa-ban-smoking' },
        { value: 'fa-ban' },
        { value: 'fa-bandage' },
        { value: 'fa-barcode' },
        { value: 'fa-bars-progress' },
        { value: 'fa-bars' },
        { value: 'fa-baseball' },
        { value: 'fa-basketball' },
        { value: 'fa-bath' },
        { value: 'fa-battery-empty' },
        { value: 'fa-battery-full' },
        { value: 'fa-battery-half' },
        { value: 'fa-bed-pulse' },
        { value: 'fa-bed' },
        { value: 'fa-bell-slash' },
        { value: 'fa-bell' },
        { value: 'fa-bezier-curve' },
        { value: 'fa-bicycle' },
        { value: 'fa-binoculars' },
        { value: 'fa-biohazard' },
        { value: 'fa-bitcoin-sign' },
        { value: 'fa-blender-phone' },
        { value: 'fa-blender' },
        { value: 'fa-blog' },
        { value: 'fa-bold' },
        { value: 'fa-bolt' },
        { value: 'fa-bomb' },
        { value: 'fa-bone' },
        { value: 'fa-bong' },
        { value: 'fa-book-atlas' },
        { value: 'fa-book-bible' },
        { value: 'fa-book-bookmark' },
        { value: 'fa-book-medical' },
        { value: 'fa-book-open' },
        { value: 'fa-book-quran' },
        { value: 'fa-book-skull' },
        { value: 'fa-book-tanakh' },
        { value: 'fa-book' },
        { value: 'fa-bookmark' },
        { value: 'fa-border-all' },
        { value: 'fa-border-none' },
        { value: 'fa-bore-hole' },
        { value: 'fa-bottle-water' },
        { value: 'fa-bowl-food' },
        { value: 'fa-bowl-rice' },
        { value: 'fa-bowling-ball' },
        { value: 'fa-box-archive' },
        { value: 'fa-box-open' },
        { value: 'fa-box-tissue' },
        { value: 'fa-box' },
        { value: 'fa-boxes-packing' },
        { value: 'fa-boxes-stacked' },
        { value: 'fa-braille' },
        { value: 'fa-brain' },
        { value: 'fa-bread-slice' },
        { value: 'fa-bridge-lock' },
        { value: 'fa-bridge-water' },
        { value: 'fa-bridge' },
        { value: 'fa-briefcase' },
        { value: 'fa-broom-ball' },
        { value: 'fa-broom' },
        { value: 'fa-brush' },
        { value: 'fa-bucket' },
        { value: 'fa-bug-slash' },
        { value: 'fa-bug' },
        { value: 'fa-bugs' },
        { value: 'fa-building-flag' },
        { value: 'fa-building-lock' },
        { value: 'fa-building-ngo' },
        { value: 'fa-building-un' },
        { value: 'fa-building-user' },
        { value: 'fa-building' },
        { value: 'fa-bullhorn' },
        { value: 'fa-bullseye' },
        { value: 'fa-burger' },
        { value: 'fa-burst' },
        { value: 'fa-bus-simple' },
        { value: 'fa-bus' },
        { value: 'fa-business-time' },
        { value: 'fa-c' },
        { value: 'fa-cable-car' },
        { value: 'fa-cake-candles' },
        { value: 'fa-calculator' },
        { value: 'fa-calendar-day' },
        { value: 'fa-calendar-days' },
        { value: 'fa-calendar-plus' },
        { value: 'fa-calendar-week' },
        { value: 'fa-calendar' },
        { value: 'fa-camera-retro' },
        { value: 'fa-camera-rotate' },
        { value: 'fa-camera' },
        { value: 'fa-campground' },
        { value: 'fa-candy-cane' },
        { value: 'fa-cannabis' },
        { value: 'fa-capsules' },
        { value: 'fa-car-battery' },
        { value: 'fa-car-burst' },
        { value: 'fa-car-on' },
        { value: 'fa-car-rear' },
        { value: 'fa-car-side' },
        { value: 'fa-car-tunnel' },
        { value: 'fa-car' },
        { value: 'fa-caravan' },
        { value: 'fa-caret-down' },
        { value: 'fa-caret-left' },
        { value: 'fa-caret-right' },
        { value: 'fa-caret-up' },
        { value: 'fa-carrot' },
        { value: 'fa-cart-flatbed' },
        { value: 'fa-cart-plus' },
        { value: 'fa-cart-shopping' },
        { value: 'fa-cash-register' },
        { value: 'fa-cat' },
        { value: 'fa-cedi-sign' },
        { value: 'fa-cent-sign' },
        { value: 'fa-certificate' },
        { value: 'fa-chair' },
        { value: 'fa-chalkboard' },
        { value: 'fa-chart-area' },
        { value: 'fa-chart-bar' },
        { value: 'fa-chart-column' },
        { value: 'fa-chart-diagram' },
        { value: 'fa-chart-gantt' },
        { value: 'fa-chart-line' },
        { value: 'fa-chart-pie' },
        { value: 'fa-chart-simple' },
        { value: 'fa-check-double' },
        { value: 'fa-check-to-slot' },
        { value: 'fa-check' },
        { value: 'fa-cheese' },
        { value: 'fa-chess-bishop' },
        { value: 'fa-chess-board' },
        { value: 'fa-chess-king' },
        { value: 'fa-chess-knight' },
        { value: 'fa-chess-pawn' },
        { value: 'fa-chess-queen' },
        { value: 'fa-chess-rook' },
        { value: 'fa-chess' },
        { value: 'fa-chevron-down' },
        { value: 'fa-chevron-left' },
        { value: 'fa-chevron-right' },
        { value: 'fa-chevron-up' },
        { value: 'fa-child-dress' },
        { value: 'fa-child' },
        { value: 'fa-children' },
        { value: 'fa-church' },
        { value: 'fa-circle-check' },
        { value: 'fa-circle-dot' },
        { value: 'fa-circle-down' },
        { value: 'fa-circle-h' },
        { value: 'fa-circle-info' },
        { value: 'fa-circle-left' },
        { value: 'fa-circle-minus' },
        { value: 'fa-circle-nodes' },
        { value: 'fa-circle-notch' },
        { value: 'fa-circle-pause' },
        { value: 'fa-circle-play' },
        { value: 'fa-circle-plus' },
        { value: 'fa-circle-right' },
        { value: 'fa-circle-stop' },
        { value: 'fa-circle-up' },
        { value: 'fa-circle-user' },
        { value: 'fa-circle-xmark' },
        { value: 'fa-circle' },
        { value: 'fa-city' },
        { value: 'fa-clapperboard' },
        { value: 'fa-clipboard' },
        { value: 'fa-clock' },
        { value: 'fa-clone' },
        { value: 'fa-cloud-bolt' },
        { value: 'fa-cloud-moon' },
        { value: 'fa-cloud-rain' },
        { value: 'fa-cloud-sun' },
        { value: 'fa-cloud' },
        { value: 'fa-clover' },
        { value: 'fa-code-branch' },
        { value: 'fa-code-commit' },
        { value: 'fa-code-compare' },
        { value: 'fa-code-fork' },
        { value: 'fa-code-merge' },
        { value: 'fa-code' },
        { value: 'fa-coins' },
        { value: 'fa-colon-sign' },
        { value: 'fa-comment-dots' },
        { value: 'fa-comment-nodes' },
        { value: 'fa-comment-slash' },
        { value: 'fa-comment-sms' },
        { value: 'fa-comment' },
        { value: 'fa-comments' },
        { value: 'fa-compact-disc' },
        { value: 'fa-compass' },
        { value: 'fa-compress' },
        { value: 'fa-computer' },
        { value: 'fa-cookie-bite' },
        { value: 'fa-cookie' },
        { value: 'fa-copy' },
        { value: 'fa-copyright' },
        { value: 'fa-couch' },
        { value: 'fa-cow' },
        { value: 'fa-credit-card' },
        { value: 'fa-crop-simple' },
        { value: 'fa-crop' },
        { value: 'fa-cross' },
        { value: 'fa-crosshairs' },
        { value: 'fa-crow' },
        { value: 'fa-crown' },
        { value: 'fa-crutch' },
        { value: 'fa-cruzeiro-sign' },
        { value: 'fa-cube' },
        { value: 'fa-cubes-stacked' },
        { value: 'fa-cubes' },
        { value: 'fa-d' },
        { value: 'fa-database' },
        { value: 'fa-delete-left' },
        { value: 'fa-democrat' },
        { value: 'fa-desktop' },
        { value: 'fa-dharmachakra' },
        { value: 'fa-diagram-next' },
        { value: 'fa-diamond' },
        { value: 'fa-dice-d20' },
        { value: 'fa-dice-d6' },
        { value: 'fa-dice-five' },
        { value: 'fa-dice-four' },
        { value: 'fa-dice-one' },
        { value: 'fa-dice-six' },
        { value: 'fa-dice-three' },
        { value: 'fa-dice-two' },
        { value: 'fa-dice' },
        { value: 'fa-disease' },
        { value: 'fa-display' },
        { value: 'fa-divide' },
        { value: 'fa-dna' },
        { value: 'fa-dog' },
        { value: 'fa-dollar-sign' },
        { value: 'fa-dolly' },
        { value: 'fa-dong-sign' },
        { value: 'fa-door-closed' },
        { value: 'fa-door-open' },
        { value: 'fa-dove' },
        { value: 'fa-down-long' },
        { value: 'fa-download' },
        { value: 'fa-dragon' },
        { value: 'fa-draw-polygon' },
        { value: 'fa-droplet-slash' },
        { value: 'fa-droplet' },
        { value: 'fa-drum-steelpan' },
        { value: 'fa-drum' },
        { value: 'fa-dumbbell' },
        { value: 'fa-dumpster-fire' },
        { value: 'fa-dumpster' },
        { value: 'fa-dungeon' },
        { value: 'fa-e' },
        { value: 'fa-ear-deaf' },
        { value: 'fa-ear-listen' },
        { value: 'fa-earth-africa' },
        { value: 'fa-earth-asia' },
        { value: 'fa-earth-europe' },
        { value: 'fa-earth-oceania' },
        { value: 'fa-egg' },
        { value: 'fa-eject' },
        { value: 'fa-elevator' },
        { value: 'fa-ellipsis' },
        { value: 'fa-envelope-open' },
        { value: 'fa-envelope' },
        { value: 'fa-equals' },
        { value: 'fa-eraser' },
        { value: 'fa-ethernet' },
        { value: 'fa-euro-sign' },
        { value: 'fa-exclamation' },
        { value: 'fa-expand' },
        { value: 'fa-explosion' },
        { value: 'fa-eye-dropper' },
        { value: 'fa-eye-slash' },
        { value: 'fa-eye' },
        { value: 'fa-f' },
        { value: 'fa-face-angry' },
        { value: 'fa-face-dizzy' },
        { value: 'fa-face-flushed' },
        { value: 'fa-face-frown' },
        { value: 'fa-face-grimace' },
        { value: 'fa-face-grin' },
        { value: 'fa-face-kiss' },
        { value: 'fa-face-laugh' },
        { value: 'fa-face-meh' },
        { value: 'fa-face-sad-cry' },
        { value: 'fa-face-sad-tear' },
        { value: 'fa-face-smile' },
        { value: 'fa-face-surprise' },
        { value: 'fa-face-tired' },
        { value: 'fa-fan' },
        { value: 'fa-faucet-drip' },
        { value: 'fa-faucet' },
        { value: 'fa-fax' },
        { value: 'fa-feather' },
        { value: 'fa-ferry' },
        { value: 'fa-file-arrow-up' },
        { value: 'fa-file-audio' },
        { value: 'fa-file-code' },
        { value: 'fa-file-contract' },
        { value: 'fa-file-csv' },
        { value: 'fa-file-excel' },
        { value: 'fa-file-export' },
        { value: 'fa-file-fragment' },
        { value: 'fa-file-image' },
        { value: 'fa-file-import' },
        { value: 'fa-file-invoice' },
        { value: 'fa-file-lines' },
        { value: 'fa-file-medical' },
        { value: 'fa-file-pdf' },
        { value: 'fa-file-pen' },
        { value: 'fa-file-shield' },
        { value: 'fa-file-video' },
        { value: 'fa-file-waveform' },
        { value: 'fa-file-word' },
        { value: 'fa-file-zipper' },
        { value: 'fa-file' },
        { value: 'fa-fill-drip' },
        { value: 'fa-fill' },
        { value: 'fa-film' },
        { value: 'fa-filter' },
        { value: 'fa-fingerprint' },
        { value: 'fa-fire-burner' },
        { value: 'fa-fire' },
        { value: 'fa-fish-fins' },
        { value: 'fa-fish' },
        { value: 'fa-flag-usa' },
        { value: 'fa-flag' },
        { value: 'fa-flask-vial' },
        { value: 'fa-flask' },
        { value: 'fa-floppy-disk' },
        { value: 'fa-florin-sign' },
        { value: 'fa-folder-closed' },
        { value: 'fa-folder-minus' },
        { value: 'fa-folder-open' },
        { value: 'fa-folder-plus' },
        { value: 'fa-folder-tree' },
        { value: 'fa-folder' },
        { value: 'fa-font-awesome' },
        { value: 'fa-font' },
        { value: 'fa-football' },
        { value: 'fa-forward-fast' },
        { value: 'fa-forward-step' },
        { value: 'fa-forward' },
        { value: 'fa-franc-sign' },
        { value: 'fa-frog' },
        { value: 'fa-futbol' },
        { value: 'fa-g' },
        { value: 'fa-gamepad' },
        { value: 'fa-gas-pump' },
        { value: 'fa-gauge-high' },
        { value: 'fa-gauge-simple' },
        { value: 'fa-gauge' },
        { value: 'fa-gavel' },
        { value: 'fa-gear' },
        { value: 'fa-gears' },
        { value: 'fa-gem' },
        { value: 'fa-genderless' },
        { value: 'fa-ghost' },
        { value: 'fa-gift' },
        { value: 'fa-gifts' },
        { value: 'fa-glass-water' },
        { value: 'fa-glasses' },
        { value: 'fa-globe' },
        { value: 'fa-golf-ball-tee' },
        { value: 'fa-gopuram' },
        { value: 'fa-greater-than' },
        { value: 'fa-grip-lines' },
        { value: 'fa-grip-vertical' },
        { value: 'fa-grip' },
        { value: 'fa-guarani-sign' },
        { value: 'fa-guitar' },
        { value: 'fa-gun' },
        { value: 'fa-h' },
        { value: 'fa-hammer' },
        { value: 'fa-hamsa' },
        { value: 'fa-hand-dots' },
        { value: 'fa-hand-fist' },
        { value: 'fa-hand-holding' },
        { value: 'fa-hand-lizard' },
        { value: 'fa-hand-peace' },
        { value: 'fa-hand-point-up' },
        { value: 'fa-hand-pointer' },
        { value: 'fa-hand-scissors' },
        { value: 'fa-hand-sparkles' },
        { value: 'fa-hand-spock' },
        { value: 'fa-hand' },
        { value: 'fa-handcuffs' },
        { value: 'fa-hands-bound' },
        { value: 'fa-hands-bubbles' },
        { value: 'fa-hands-holding' },
        { value: 'fa-hands-praying' },
        { value: 'fa-hands' },
        { value: 'fa-handshake' },
        { value: 'fa-hanukiah' },
        { value: 'fa-hard-drive' },
        { value: 'fa-hashtag' },
        { value: 'fa-hat-cowboy' },
        { value: 'fa-hat-wizard' },
        { value: 'fa-heading' },
        { value: 'fa-headphones' },
        { value: 'fa-headset' },
        { value: 'fa-heart-crack' },
        { value: 'fa-heart-pulse' },
        { value: 'fa-heart' },
        { value: 'fa-helicopter' },
        { value: 'fa-helmet-safety' },
        { value: 'fa-helmet-un' },
        { value: 'fa-hexagon-nodes' },
        { value: 'fa-highlighter' },
        { value: 'fa-hippo' },
        { value: 'fa-hockey-puck' },
        { value: 'fa-holly-berry' },
        { value: 'fa-horse-head' },
        { value: 'fa-horse' },
        { value: 'fa-hospital-user' },
        { value: 'fa-hospital' },
        { value: 'fa-hotdog' },
        { value: 'fa-hotel' },
        { value: 'fa-hourglass-end' },
        { value: 'fa-hourglass' },
        { value: 'fa-house-chimney' },
        { value: 'fa-house-crack' },
        { value: 'fa-house-fire' },
        { value: 'fa-house-flag' },
        { value: 'fa-house-laptop' },
        { value: 'fa-house-lock' },
        { value: 'fa-house-medical' },
        { value: 'fa-house-signal' },
        { value: 'fa-house-tsunami' },
        { value: 'fa-house-user' },
        { value: 'fa-house' },
        { value: 'fa-hryvnia-sign' },
        { value: 'fa-hurricane' },
        { value: 'fa-i-cursor' },
        { value: 'fa-i' },
        { value: 'fa-ice-cream' },
        { value: 'fa-icicles' },
        { value: 'fa-icons' },
        { value: 'fa-id-badge' },
        { value: 'fa-id-card-clip' },
        { value: 'fa-id-card' },
        { value: 'fa-igloo' },
        { value: 'fa-image' },
        { value: 'fa-images' },
        { value: 'fa-inbox' },
        { value: 'fa-indent' },
        { value: 'fa-industry' },
        { value: 'fa-infinity' },
        { value: 'fa-info' },
        { value: 'fa-italic' },
        { value: 'fa-j' },
        { value: 'fa-jar-wheat' },
        { value: 'fa-jar' },
        { value: 'fa-jedi' },
        { value: 'fa-jet-fighter' },
        { value: 'fa-joint' },
        { value: 'fa-jug-detergent' },
        { value: 'fa-k' },
        { value: 'fa-kaaba' },
        { value: 'fa-key' },
        { value: 'fa-keyboard' },
        { value: 'fa-khanda' },
        { value: 'fa-kip-sign' },
        { value: 'fa-kit-medical' },
        { value: 'fa-kitchen-set' },
        { value: 'fa-kiwi-bird' },
        { value: 'fa-l' },
        { value: 'fa-land-mine-on' },
        { value: 'fa-landmark-dome' },
        { value: 'fa-landmark-flag' },
        { value: 'fa-landmark' },
        { value: 'fa-language' },
        { value: 'fa-laptop-code' },
        { value: 'fa-laptop-file' },
        { value: 'fa-laptop' },
        { value: 'fa-lari-sign' },
        { value: 'fa-layer-group' },
        { value: 'fa-leaf' },
        { value: 'fa-left-long' },
        { value: 'fa-left-right' },
        { value: 'fa-lemon' },
        { value: 'fa-less-than' },
        { value: 'fa-life-ring' },
        { value: 'fa-lightbulb' },
        { value: 'fa-lines-leaning' },
        { value: 'fa-link-slash' },
        { value: 'fa-link' },
        { value: 'fa-lira-sign' },
        { value: 'fa-list-check' },
        { value: 'fa-list-ol' },
        { value: 'fa-list-ul' },
        { value: 'fa-list' },
        { value: 'fa-litecoin-sign' },
        { value: 'fa-location-dot' },
        { value: 'fa-location-pin' },
        { value: 'fa-lock-open' },
        { value: 'fa-lock' },
        { value: 'fa-locust' },
        { value: 'fa-lungs-virus' },
        { value: 'fa-lungs' },
        { value: 'fa-m' },
        { value: 'fa-magnet' },
        { value: 'fa-manat-sign' },
        { value: 'fa-map-location' },
        { value: 'fa-map-pin' },
        { value: 'fa-map' },
        { value: 'fa-marker' },
        { value: 'fa-mars-double' },
        { value: 'fa-mars-stroke' },
        { value: 'fa-mars' },
        { value: 'fa-martini-glass' },
        { value: 'fa-mask-face' },
        { value: 'fa-mask' },
        { value: 'fa-masks-theater' },
        { value: 'fa-maximize' },
        { value: 'fa-medal' },
        { value: 'fa-memory' },
        { value: 'fa-menorah' },
        { value: 'fa-mercury' },
        { value: 'fa-message' },
        { value: 'fa-meteor' },
        { value: 'fa-microchip' },
        { value: 'fa-microphone' },
        { value: 'fa-microscope' },
        { value: 'fa-mill-sign' },
        { value: 'fa-minimize' },
        { value: 'fa-minus' },
        { value: 'fa-mitten' },
        { value: 'fa-mobile-button' },
        { value: 'fa-mobile-retro' },
        { value: 'fa-mobile-screen' },
        { value: 'fa-mobile' },
        { value: 'fa-money-bill-1' },
        { value: 'fa-money-bill' },
        { value: 'fa-money-bills' },
        { value: 'fa-money-check' },
        { value: 'fa-monument' },
        { value: 'fa-moon' },
        { value: 'fa-mortar-pestle' },
        { value: 'fa-mosque' },
        { value: 'fa-mosquito-net' },
        { value: 'fa-mosquito' },
        { value: 'fa-motorcycle' },
        { value: 'fa-mound' },
        { value: 'fa-mountain-city' },
        { value: 'fa-mountain-sun' },
        { value: 'fa-mountain' },
        { value: 'fa-mug-hot' },
        { value: 'fa-mug-saucer' },
        { value: 'fa-music' },
        { value: 'fa-n' },
        { value: 'fa-naira-sign' },
        { value: 'fa-network-wired' },
        { value: 'fa-neuter' },
        { value: 'fa-newspaper' },
        { value: 'fa-not-equal' },
        { value: 'fa-notdef' },
        { value: 'fa-note-sticky' },
        { value: 'fa-notes-medical' },
        { value: 'fa-o' },
        { value: 'fa-object-group' },
        { value: 'fa-oil-can' },
        { value: 'fa-oil-well' },
        { value: 'fa-om' },
        { value: 'fa-otter' },
        { value: 'fa-outdent' },
        { value: 'fa-p' },
        { value: 'fa-pager' },
        { value: 'fa-paint-roller' },
        { value: 'fa-paintbrush' },
        { value: 'fa-palette' },
        { value: 'fa-pallet' },
        { value: 'fa-panorama' },
        { value: 'fa-paper-plane' },
        { value: 'fa-paperclip' },
        { value: 'fa-parachute-box' },
        { value: 'fa-paragraph' },
        { value: 'fa-passport' },
        { value: 'fa-paste' },
        { value: 'fa-pause' },
        { value: 'fa-paw' },
        { value: 'fa-peace' },
        { value: 'fa-pen-clip' },
        { value: 'fa-pen-fancy' },
        { value: 'fa-pen-nib' },
        { value: 'fa-pen-ruler' },
        { value: 'fa-pen-to-square' },
        { value: 'fa-pen' },
        { value: 'fa-pencil' },
        { value: 'fa-people-arrows' },
        { value: 'fa-people-group' },
        { value: 'fa-people-line' },
        { value: 'fa-people-roof' },
        { value: 'fa-pepper-hot' },
        { value: 'fa-percent' },
        { value: 'fa-person-biking' },
        { value: 'fa-person-booth' },
        { value: 'fa-person-burst' },
        { value: 'fa-person-cane' },
        { value: 'fa-person-dress' },
        { value: 'fa-person-hiking' },
        { value: 'fa-person-rays' },
        { value: 'fa-person-rifle' },
        { value: 'fa-person-skiing' },
        { value: 'fa-person' },
        { value: 'fa-peseta-sign' },
        { value: 'fa-peso-sign' },
        { value: 'fa-phone-flip' },
        { value: 'fa-phone-slash' },
        { value: 'fa-phone-volume' },
        { value: 'fa-phone' },
        { value: 'fa-photo-film' },
        { value: 'fa-piggy-bank' },
        { value: 'fa-pills' },
        { value: 'fa-pizza-slice' },
        { value: 'fa-plane-arrival' },
        { value: 'fa-plane-lock' },
        { value: 'fa-plane-slash' },
        { value: 'fa-plane-up' },
        { value: 'fa-plane' },
        { value: 'fa-plant-wilt' },
        { value: 'fa-plate-wheat' },
        { value: 'fa-play' },
        { value: 'fa-plug' },
        { value: 'fa-plus-minus' },
        { value: 'fa-plus' },
        { value: 'fa-podcast' },
        { value: 'fa-poo-storm' },
        { value: 'fa-poo' },
        { value: 'fa-poop' },
        { value: 'fa-power-off' },
        { value: 'fa-prescription' },
        { value: 'fa-print' },
        { value: 'fa-pump-medical' },
        { value: 'fa-pump-soap' },
        { value: 'fa-puzzle-piece' },
        { value: 'fa-q' },
        { value: 'fa-qrcode' },
        { value: 'fa-question' },
        { value: 'fa-quote-left' },
        { value: 'fa-quote-right' },
        { value: 'fa-r' },
        { value: 'fa-radiation' },
        { value: 'fa-radio' },
        { value: 'fa-rainbow' },
        { value: 'fa-ranking-star' },
        { value: 'fa-receipt' },
        { value: 'fa-record-vinyl' },
        { value: 'fa-rectangle-ad' },
        { value: 'fa-recycle' },
        { value: 'fa-registered' },
        { value: 'fa-repeat' },
        { value: 'fa-reply-all' },
        { value: 'fa-reply' },
        { value: 'fa-republican' },
        { value: 'fa-restroom' },
        { value: 'fa-retweet' },
        { value: 'fa-ribbon' },
        { value: 'fa-right-left' },
        { value: 'fa-right-long' },
        { value: 'fa-ring' },
        { value: 'fa-road-barrier' },
        { value: 'fa-road-bridge' },
        { value: 'fa-road-lock' },
        { value: 'fa-road-spikes' },
        { value: 'fa-road' },
        { value: 'fa-robot' },
        { value: 'fa-rocket' },
        { value: 'fa-rotate-left' },
        { value: 'fa-rotate-right' },
        { value: 'fa-rotate' },
        { value: 'fa-route' },
        { value: 'fa-rss' },
        { value: 'fa-ruble-sign' },
        { value: 'fa-rug' },
        { value: 'fa-ruler' },
        { value: 'fa-rupee-sign' },
        { value: 'fa-rupiah-sign' },
        { value: 'fa-s' },
        { value: 'fa-sack-dollar' },
        { value: 'fa-sack-xmark' },
        { value: 'fa-sailboat' },
        { value: 'fa-satellite' },
        { value: 'fa-school-flag' },
        { value: 'fa-school-lock' },
        { value: 'fa-school' },
        { value: 'fa-scissors' },
        { value: 'fa-screwdriver' },
        { value: 'fa-scroll-torah' },
        { value: 'fa-scroll' },
        { value: 'fa-sd-card' },
        { value: 'fa-section' },
        { value: 'fa-seedling' },
        { value: 'fa-server' },
        { value: 'fa-shapes' },
        { value: 'fa-share-nodes' },
        { value: 'fa-share' },
        { value: 'fa-sheet-plastic' },
        { value: 'fa-shekel-sign' },
        { value: 'fa-shield-cat' },
        { value: 'fa-shield-dog' },
        { value: 'fa-shield-halved' },
        { value: 'fa-shield-heart' },
        { value: 'fa-shield-virus' },
        { value: 'fa-shield' },
        { value: 'fa-ship' },
        { value: 'fa-shirt' },
        { value: 'fa-shoe-prints' },
        { value: 'fa-shop-lock' },
        { value: 'fa-shop-slash' },
        { value: 'fa-shop' },
        { value: 'fa-shower' },
        { value: 'fa-shrimp' },
        { value: 'fa-shuffle' },
        { value: 'fa-shuttle-space' },
        { value: 'fa-sign-hanging' },
        { value: 'fa-signal' },
        { value: 'fa-signature' },
        { value: 'fa-signs-post' },
        { value: 'fa-sim-card' },
        { value: 'fa-sink' },
        { value: 'fa-sitemap' },
        { value: 'fa-skull' },
        { value: 'fa-slash' },
        { value: 'fa-sleigh' },
        { value: 'fa-sliders' },
        { value: 'fa-smog' },
        { value: 'fa-smoking' },
        { value: 'fa-snowflake' },
        { value: 'fa-snowman' },
        { value: 'fa-snowplow' },
        { value: 'fa-soap' },
        { value: 'fa-socks' },
        { value: 'fa-solar-panel' },
        { value: 'fa-sort-down' },
        { value: 'fa-sort-up' },
        { value: 'fa-sort' },
        { value: 'fa-spa' },
        { value: 'fa-spell-check' },
        { value: 'fa-spider' },
        { value: 'fa-spinner' },
        { value: 'fa-splotch' },
        { value: 'fa-spoon' },
        { value: 'fa-spray-can' },
        { value: 'fa-square-binary' },
        { value: 'fa-square-check' },
        { value: 'fa-square-full' },
        { value: 'fa-square-h' },
        { value: 'fa-square-minus' },
        { value: 'fa-square-nfi' },
        { value: 'fa-square-pen' },
        { value: 'fa-square-phone' },
        { value: 'fa-square-plus' },
        { value: 'fa-square-rss' },
        { value: 'fa-square-virus' },
        { value: 'fa-square-xmark' },
        { value: 'fa-square' },
        { value: 'fa-staff-snake' },
        { value: 'fa-stairs' },
        { value: 'fa-stamp' },
        { value: 'fa-stapler' },
        { value: 'fa-star-half' },
        { value: 'fa-star-of-david' },
        { value: 'fa-star-of-life' },
        { value: 'fa-star' },
        { value: 'fa-sterling-sign' },
        { value: 'fa-stethoscope' },
        { value: 'fa-stop' },
        { value: 'fa-stopwatch-20' },
        { value: 'fa-stopwatch' },
        { value: 'fa-store-slash' },
        { value: 'fa-store' },
        { value: 'fa-street-view' },
        { value: 'fa-strikethrough' },
        { value: 'fa-stroopwafel' },
        { value: 'fa-subscript' },
        { value: 'fa-suitcase' },
        { value: 'fa-sun' },
        { value: 'fa-superscript' },
        { value: 'fa-swatchbook' },
        { value: 'fa-synagogue' },
        { value: 'fa-syringe' },
        { value: 'fa-t' },
        { value: 'fa-table-cells' },
        { value: 'fa-table-columns' },
        { value: 'fa-table-list' },
        { value: 'fa-table' },
        { value: 'fa-tablet-button' },
        { value: 'fa-tablet' },
        { value: 'fa-tablets' },
        { value: 'fa-tag' },
        { value: 'fa-tags' },
        { value: 'fa-tape' },
        { value: 'fa-tarp-droplet' },
        { value: 'fa-tarp' },
        { value: 'fa-taxi' },
        { value: 'fa-teeth-open' },
        { value: 'fa-teeth' },
        { value: 'fa-tenge-sign' },
        { value: 'fa-tent' },
        { value: 'fa-tents' },
        { value: 'fa-terminal' },
        { value: 'fa-text-height' },
        { value: 'fa-text-slash' },
        { value: 'fa-text-width' },
        { value: 'fa-thermometer' },
        { value: 'fa-thumbs-down' },
        { value: 'fa-thumbs-up' },
        { value: 'fa-thumbtack' },
        { value: 'fa-ticket-simple' },
        { value: 'fa-ticket' },
        { value: 'fa-timeline' },
        { value: 'fa-toggle-off' },
        { value: 'fa-toggle-on' },
        { value: 'fa-toilet-paper' },
        { value: 'fa-toilet' },
        { value: 'fa-toolbox' },
        { value: 'fa-tooth' },
        { value: 'fa-torii-gate' },
        { value: 'fa-tornado' },
        { value: 'fa-tower-cell' },
        { value: 'fa-tractor' },
        { value: 'fa-trademark' },
        { value: 'fa-traffic-light' },
        { value: 'fa-trailer' },
        { value: 'fa-train-subway' },
        { value: 'fa-train-tram' },
        { value: 'fa-train' },
        { value: 'fa-transgender' },
        { value: 'fa-trash-can' },
        { value: 'fa-trash' },
        { value: 'fa-tree-city' },
        { value: 'fa-tree' },
        { value: 'fa-trophy' },
        { value: 'fa-trowel-bricks' },
        { value: 'fa-trowel' },
        { value: 'fa-truck-droplet' },
        { value: 'fa-truck-fast' },
        { value: 'fa-truck-field' },
        { value: 'fa-truck-front' },
        { value: 'fa-truck-medical' },
        { value: 'fa-truck-monster' },
        { value: 'fa-truck-moving' },
        { value: 'fa-truck-pickup' },
        { value: 'fa-truck-plane' },
        { value: 'fa-truck' },
        { value: 'fa-tty' },
        { value: 'fa-turn-down' },
        { value: 'fa-turn-up' },
        { value: 'fa-tv' },
        { value: 'fa-u' },
        { value: 'fa-umbrella' },
        { value: 'fa-underline' },
        { value: 'fa-unlock' },
        { value: 'fa-up-down' },
        { value: 'fa-up-long' },
        { value: 'fa-upload' },
        { value: 'fa-user-check' },
        { value: 'fa-user-clock' },
        { value: 'fa-user-doctor' },
        { value: 'fa-user-gear' },
        { value: 'fa-user-graduate' },
        { value: 'fa-user-group' },
        { value: 'fa-user-injured' },
        { value: 'fa-user-large' },
        { value: 'fa-user-lock' },
        { value: 'fa-user-minus' },
        { value: 'fa-user-ninja' },
        { value: 'fa-user-nurse' },
        { value: 'fa-user-pen' },
        { value: 'fa-user-plus' },
        { value: 'fa-user-secret' },
        { value: 'fa-user-shield' },
        { value: 'fa-user-slash' },
        { value: 'fa-user-tag' },
        { value: 'fa-user-tie' },
        { value: 'fa-user-xmark' },
        { value: 'fa-user' },
        { value: 'fa-users-gear' },
        { value: 'fa-users-line' },
        { value: 'fa-users-rays' },
        { value: 'fa-users-slash' },
        { value: 'fa-users' },
        { value: 'fa-utensils' },
        { value: 'fa-v' },
        { value: 'fa-van-shuttle' },
        { value: 'fa-vault' },
        { value: 'fa-vector-square' },
        { value: 'fa-venus-double' },
        { value: 'fa-venus-mars' },
        { value: 'fa-venus' },
        { value: 'fa-vest-patches' },
        { value: 'fa-vest' },
        { value: 'fa-vial-virus' },
        { value: 'fa-vial' },
        { value: 'fa-vials' },
        { value: 'fa-video-slash' },
        { value: 'fa-video' },
        { value: 'fa-vihara' },
        { value: 'fa-virus-covid' },
        { value: 'fa-virus-slash' },
        { value: 'fa-virus' },
        { value: 'fa-viruses' },
        { value: 'fa-voicemail' },
        { value: 'fa-volcano' },
        { value: 'fa-volleyball' },
        { value: 'fa-volume-high' },
        { value: 'fa-volume-low' },
        { value: 'fa-volume-off' },
        { value: 'fa-volume-xmark' },
        { value: 'fa-vr-cardboard' },
        { value: 'fa-w' },
        { value: 'fa-walkie-talkie' },
        { value: 'fa-wallet' },
        { value: 'fa-wand-magic' },
        { value: 'fa-warehouse' },
        { value: 'fa-water-ladder' },
        { value: 'fa-water' },
        { value: 'fa-wave-square' },
        { value: 'fa-web-awesome' },
        { value: 'fa-weight-scale' },
        { value: 'fa-wheat-awn' },
        { value: 'fa-wheelchair' },
        { value: 'fa-wifi' },
        { value: 'fa-wifi' },
        { value: 'fa-wind' },
        { value: 'fa-wine-bottle' },
        { value: 'fa-wine-glass' },
        { value: 'fa-won-sign' },
        { value: 'fa-worm' },
        { value: 'fa-wrench' },
        { value: 'fa-x-ray' },
        { value: 'fa-x' },
        { value: 'fa-xmark' },
        { value: 'fa-xmarks-lines' },
        { value: 'fa-y' },
        { value: 'fa-yen-sign' },
        { value: 'fa-yin-yang' },
        { value: 'fa-z' },
];

export default iconOptions;